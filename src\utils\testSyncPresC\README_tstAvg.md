# TST平均对象创建功能说明

## 功能概述

`tstAvgCreateF.m` 函数实现了基于多个tst对象创建平均对象的功能。该功能以tst01为基础，将关键压力测量值取平均，然后重新计算相关的缩尺比例属性。

## 主要特性

### 1. 智能平均策略
- **基础对象**：以tst01为基础，保持所有基本属性不变
- **关键压力值平均**：对pTotalMeanAll、pStcMeanAll、pDynMeanAll取多个对象的算术平均值
- **自动重新计算**：使用modelScaleM函数重新计算相关的缩尺比例属性

### 2. 数据质量评估
- **变异系数计算**：自动计算各压力值的变异系数
- **重现性评估**：根据变异系数评估数据重现性（优秀/良好/一般/较差）
- **统计信息**：提供详细的统计分析结果

### 3. 完整的信息记录
- **创建信息**：记录创建时间、原始文件列表
- **原始数据**：保存所有原始压力值用于对比
- **分离存储**：避免类属性限制，信息存储在单独文件中

## 使用方法

### 基本用法
```matlab
% 创建基于4个tst对象的平均对象
tst = tstAvgCreateF(4, 'flagRun', 1);

% 保存平均对象
save('.\mat\tst.mat', 'tst');

% 展示详细信息
tstAvgShowF();
```

### 参数说明
- `nTst`: 要平均的tst对象数量
- `flagRun`: 是否执行创建（0=不执行，1=执行）

## 实际效果

基于您项目中的4个tst对象，创建的平均对象具有以下特征：

### 压力值平均结果
```
原始值 → 平均值
pTotalMeanAll: [44.27, 41.86, 41.71, 41.71] → 42.39 Pa
pStcMeanAll:   [-0.19, 0.76, 0.73, 0.73]   → 0.51 Pa  
pDynMeanAll:   [46.69, 43.16, 43.03, 43.03] → 43.97 Pa
```

### 数据质量评估
- **动压重现性**: 优秀 (变异系数: 4.12%)
- **总压重现性**: 优秀 (变异系数: 2.97%)

### 保持不变的属性
- 采样频率: 330 Hz
- 时间步长: 0.003030 s
- 参考高度: 146 m
- 试验编号: 1 (来自tst01)
- 试验日期: 20250429 (来自tst01)

## 文件结构

### 输出文件
- `tst.mat`: 平均tst对象
- `tstAvgInfo.mat`: 平均信息和统计数据

### 相关函数
- `tstAvgCreateF.m`: 主创建函数
- `tstAvgShowF.m`: 详细信息展示函数

## 优势

### 1. 提高数据可靠性
- 通过多次试验平均，减少随机误差
- 提高压力测量值的准确性
- 保持试验的重现性

### 2. 简化后续分析
- 统一的tst对象，避免选择困难
- 保持与原有代码的兼容性
- 减少数据管理复杂度

### 3. 质量控制
- 自动计算变异系数
- 评估试验重现性
- 识别异常数据

## 应用场景

### 1. 风洞试验数据处理
- 多次重复试验的数据合并
- 提高测量精度
- 减少不确定度

### 2. 工程设计应用
- 获得更可靠的设计参数
- 提高设计安全性
- 符合工程实践要求

### 3. 研究分析
- 试验重现性分析
- 数据质量评估
- 不确定度量化

## 注意事项

1. **文件依赖**: 确保所有原始tst对象文件存在
2. **模型缩尺**: modelScaleM函数可能需要额外的依赖函数
3. **类属性限制**: 创建信息存储在单独文件中，避免类属性限制
4. **数据备份**: 建议保留原始tst01-tst04文件作为备份

## 集成到主程序

该功能已集成到主程序 `mainWTXMXC2.m` 中：

```matlab
%% -------------------- 平均对象创建 ---------------------------
tst = tstAvgCreateF(prj.nTst,'flagRun',1);              % 创建平均对象
save('.\mat\tst','tst');                                % 保存平均对象
tstAvgShowF();                                          % 展示详细信息
```

后续所有使用tst对象的地方都会自动使用这个平均对象，无需修改其他代码。

## 更新日志

- **2024-12-20**: 初始版本发布
  - 实现基本平均功能
  - 添加数据质量评估
  - 创建详细展示功能
  - 集成到主程序流程

## 作者

@lichaosz 20241220
