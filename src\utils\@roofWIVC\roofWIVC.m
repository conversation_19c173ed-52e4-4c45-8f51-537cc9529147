classdef roofWIVC < prjWTTC
    % roof information for Wind Induced Vibration --- Class 建筑风振分析的基本信息
    
    properties
        bound
        coefAmp         % 1.0 | 当等效风荷载（基底弯矩）小于规范值的80%时，用基底弯矩的比值放大风荷载时程重新计算等效风荷载载。
        flgCrsESWLYJK
        flagTrans2FEACS % 是否将坐标从TAPCFD坐标系转换到FEA坐标系
        gamma
        gamma1          % 测点（CFD网格）坐标系与结构坐标系的逆时针夹角
        gamma2          % 测点坐标系与风向坐标系的逆时针夹角
        iBld            % 4 | 同一个prj中此建筑结构的序号
        iBldStr         % 同一个prj中此建筑结构的序号的字符变量
        iManFlr         % 4 | 人居最高层，注意此处楼层为 从上向下 计数，43自由度为44楼层顶板（公寓楼层）
        iTowerYJK
        M
        mNode
        nameCN
        nameEN
        nameENCell      % 想办法弃用该变量
        nBoundCFD       % CFD导出网格边界的数量，和封闭屋盖的数量一致
        nDof
        nFlr
        nMode
        nNode
        node
        nTower
        omg
        pathBld
        pathClad
        pathESWL
        pathGeom
        pathLoad
        pathRsp
        pathStruDyn
		phi
        rptXls          % 结构的试验结果文件名
		swHistSave
        T
        xdCtr           % [20  25 -25 -20] | 建筑物 舒适度角点 与 刚（质/扭转中）心  X向的距离,米,（角点可以是多个）
        xyPInTAPCFD        % 参考点P在 测点坐标系 或 CFD坐标系 中的坐标
        xyPInFEA           % 参考点P在FEA中的坐标
        xyOTapInFEA
        xyTapInFEA
        xyzMaxFEA
        xyzMinFEA
        ydCtr           % [11 -11 -11 -11] | 建筑物 舒适度角点 与 刚（质/扭转中）心  Y向的距离,米
        zFlr
        zLyr
    end
    
    properties(Constant)
        nDofNode   = 3;                   % 每个楼层 3 个自由度
        swAnlsCell = {'Time';'Freq'};     % 风振分析方法
    end
    
	properties (Dependent)
        zeta         % 依据 重现期 确定的阻尼比
    end
    
    methods
        function obj = roofWIVC(iBld,fileTstInfo,varargin)
            defaultFlgBldDynRead  = 1;        % 默认重新读取结构信息并绘制示意图
            defaultFlgPlotFlrArea  = 1;        % 默认绘制示意图
            p = inputParser;
            p.addRequired('iBld', @isnumeric);  % isobject
            p.addRequired('fileTstInfo', @ischar);  % ischar
            validScalarPosNum = @(x) isnumeric(x);
            p.addParameter('flgBldDynRead',defaultFlgBldDynRead,validScalarPosNum);
            p.addParameter('flgPlotFlrArea',defaultFlgPlotFlrArea,validScalarPosNum); 
            p.addParameter('tap',[],@isobject); 
            p.parse(iBld,fileTstInfo,varargin{:});
            flgBldDynRead = p.Results.flgBldDynRead;
            % flgPlotFlrArea = p.Results.flgPlotFlrArea;
            tap = p.Results.tap;

            obj = obj@prjWTTC(fileTstInfo);   % 复制项目信息
            
            obj.iBld    = iBld; 
            obj.iBldStr = num2str(iBld,'%02i');
            obj.nameCN  = obj.nameBldAllCN{iBld};
            obj.nameEN  = obj.nameBldAllEN{iBld};
            obj.rptXls      = [pwd '\项目试验结果_' obj.namePrjCN '_' obj.nameCN '.xlsx'];

            chkMkdirF(['./' obj.iBldStr '/']);

            obj.pathBld     = ['./' obj.iBldStr '/'];                % 结构文件 路径
            obj.pathGeom    = ['./' obj.iBldStr '/00结构几何/'];     % 结构几何文件 路径
            obj.pathClad    = ['./' obj.iBldStr '/01覆面风压/'];     % 结构几何文件 路径
            obj.pathStruDyn = ['./' obj.iBldStr '/02结构特性/'];     % 结构动力特性文件 路径
            obj.pathLoad    = ['./' obj.iBldStr '/03荷载时程/'];     % 层荷载文件 路径
            obj.pathRsp     = ['./' obj.iBldStr '/04风振响应/'];     % 响应文件 路径
            obj.pathESWL    = ['./' obj.iBldStr '/05等效荷载/'];     % 响应文件 路径
            chkMkdirF([obj.pathGeom 'figures'],[obj.pathClad 'figures'],[obj.pathStruDyn 'figures'],...
                      [obj.pathLoad 'figures'],[obj.pathRsp 'figures'],[obj.pathESWL 'figures']);

            tmpBld = readtable(['./' obj.iBldStr '/结构几何信息_' obj.namePrjCN '_' obj.iBldStr],'sheet','屋盖信息','NumHeaderLines',2);
            obj.iTowerYJK = rmmissing(tmpBld.iTowerYJK);
            obj.nTower    = length(obj.iTowerYJK);
            [node,phi,M,T,mNode] = midas2LMMF(obj,flgBldDynRead);

            obj.node    = node;
            obj.phi     = phi;
            obj.M       = M;
            obj.T       = T;
            obj.omg     = 2*pi./T;                % 结构周期
            obj.nMode   = length(T);              % 有限元软件中导出的所有的模态阶数
            obj.nNode   = length(M)/obj.nDofNode;                % 结构结点数量
            obj.nDof    = length(M);                 % 自由度个数
            obj.mNode   = mNode;

            obj.xyPInTAPCFD   = [rmmissing(tmpBld.xPInTAPCFD) rmmissing(tmpBld.yPInTAPCFD)];
            obj.xyPInFEA      = [rmmissing(tmpBld.xPInFEA) rmmissing(tmpBld.yPInFEA)];
            obj.xyzMinFEA     = rmmissing(tmpBld.xyzMinFEA);
            obj.xyzMaxFEA     = rmmissing(tmpBld.xyzMaxFEA);
            obj.gamma1        = rmmissing(tmpBld.gamma1);
            obj.gamma2        = rmmissing(tmpBld.gamma2);
            obj.nBoundCFD     = rmmissing(tmpBld.nBoundCFD);
            obj.flagTrans2FEACS  = logical(rmmissing(tmpBld.flagTrans2FEACS));
            tmp1 = readtable(['./' obj.iBldStr '/结构几何信息_' obj.namePrjCN '_' obj.iBldStr],'sheet','风振分析','NumHeaderLines',2);   % [strSide 'Z+'] nameBounds{k}{i} 
            obj.swHistSave    = tmp1.swHistSave;
            obj.flgCrsESWLYJK = tmp1.flgCrsESWLYJK;

            % 读取结构在不同视角下的轮廓
            if obj.wtC == 1
                obj.bound = readBldBound(obj);
            elseif obj.wtC == 2    % 将在CFD网格坐标系下的bound平移到有限元坐标系下，因为最终结果的展示也是在有限元node的坐标系中展示
                obj.bound = readBldBound(obj,'flagTrans2FEACS',obj.flagTrans2FEACS);
            end
        end
        
        function zeta = get.zeta(obj)
            switch obj.RP
                case 10
                    zeta = obj.zetaAcc;
                case 50
                    zeta = obj.zetaMain;
                case 100
                    zeta = obj.zetaMain;
            end
        end
    end
end
        
 