function [] = plotGeomTBF(bld,varargin)
% 绘制建筑轮廓及楼层高度，对比、检查建筑模型与结构模型中几何信息的差异
%
% 说明：
% 建筑的轮廓信息由建筑模型导出，而楼层信息由结构模型导出，其中可能存在误差，需要对比找出差异并解决
% 
% 举例：
% plotGeomTBF(bld,'sizeFig',[16 16],'flagPlot',1);

% @lichaosz 20230817

defaultSizeFig = [15 25];  % 默认 word 中A4除去2.5左右边距，则图片的宽度最大值为 21-5 = 15 cm，图片最大高度为29.7-5 = 24 cm
defaultFlagPlot   = 0;

p = inputParser;
p.addRequired('bld', @isobject);
p.addParameter('sizeFig',defaultSizeFig,@isnumeric);
p.addParameter('flagPlot',defaultFlagPlot,@isnumeric);
p.parse(bld, varargin{:});
sizeFig  = p.Results.sizeFig;

if p.Results.flagPlot == 0; disp(['不重新绘制 ' bld.iBldStr '('  bld.nameCN ') 结构楼层高度、宽度对比图']);return; end

widthLine = 2;
lExt = 10;

for iView = 1:4
    bound = bld.bound{iView};
    
    [ix, iy, ~, ~] = Dim2F(iView);
    
    fontSize = 6;
    posAx = [0.05,0.05,0.9,0.93];
    figure
    
    % 找到图像的最大最小xy坐标
    xMax = max(bound{1}.outer(:,ix));
    xMin = min(bound{1}.outer(:,ix));
    yMax = max(bound{1}.outer(:,iy));
    yMin = min(bound{1}.outer(:,iy));
    
    for i = 1:length(bound)
        xMax = max([xMax;bound{i}.outer(:,ix)]);
        xMin = min([xMin;bound{i}.outer(:,ix)]);
        yMax = max([yMax;bound{i}.outer(:,iy)]);
        yMin = min([yMin;bound{i}.outer(:,iy)]);
    end
    
    % 当有建筑整体辅助线时，需要在图片中计及其坐标
    if isfield(bound{1},'astW')
        for j = 1:length(bound{1}.astW)
            xMax = max([xMax;bound{1}.astW{j}(:,ix)]);
            xMin = min([xMin;bound{1}.astW{j}(:,ix)]);
            yMax = max([yMax;bound{1}.astW{j}(:,iy)]);
            yMin = min([yMin;bound{1}.astW{j}(:,iy)]);
        end
    end

    if bld.typeStrct == 1
        fig.height = sizeFig(2);   % 图片宽度
        fig.width  = fig.height/(yMax-yMin+2*lExt)*(xMax-xMin+2*lExt)*1.3;
    elseif bld.typeStrct == 2
        fig.width  = sizeFig(1);   % 图片宽度    
        fig.height = fig.width/(xMax-xMin)*(yMax-yMin);
    end
    text(mean([xMin,xMax]),mean([yMin,yMax]),bound{1}.class,'FontAngle','italic','Color','r','FontSize',5*fontSize,'HorizontalAlignment','center');hold on;

    % 绘制轮廓，包括外轮廓，内轮廓，辅助线，建筑轮廓（辅助定位）
    for k = 1:length(bound)
        xBound = bound{k}.outer(:,ix);
        yBound = bound{k}.outer(:,iy);
        
        if any(isnan(xBound))
            ind=find(isnan(xBound));
            plot([xBound(1:ind-1);xBound(1)],[yBound(1:ind-1);yBound(1)],'-k','linewidth',widthLine);hold on;
            plot([xBound(ind+1:end);xBound(ind+1)],[yBound(ind+1:end);yBound(ind+1)],'-k','linewidth',widthLine);hold on;
        else
            hP(1) = plot([xBound;xBound(1)],[yBound;yBound(1)],'-k','linewidth',widthLine);hold on;
            if strcmp(bound{k}.nameShow,'on') && length(bound{k})>1
                text((max(xBound)+min(xBound))/2,(max(yBound)+min(yBound))/2,bound{k}.name,'FontAngle','italic','Color','b','FontSize',3*fontSize,'HorizontalAlignment','center'); 
            end
        end
        if isfield(bound{k},'astR')   % 面域轮廓辅助线
            for j = 1:length(bound{k}.astR)
                plot(bound{k}.astR{j}(:,ix),bound{k}.astR{j}(:,iy),'-k','linewidth',0.5*widthLine);hold on;
            end
        end
    end
    
    if isfield(bound{1},'astW')   % 整体辅助线
        for j = 1:length(bound{1}.astW)
            plot([bound{1}.astW{j}(:,ix);bound{1}.astW{j}(1,ix)],[bound{1}.astW{j}(:,iy);bound{1}.astW{j}(1,iy)],'-','color',[0.5 0.5 0.5],'linewidth',0.5*widthLine);hold on;
        end
    end

    % 绘制楼层高度线
    for i = 1:bld.nFlr
        hP(2) = plot([xMin xMax],bld.flrT.z(i).*[1 1],'--b');hold on;
        if (iView==1 || iView==4)
            text(xMin-lExt,bld.flrT.z(i),[num2str(bld.flrT.iFlrArch(i),'%02i')]);
            text(xMax+lExt/5,bld.flrT.z(i),[num2str(bld.flrT.z(i),'%04.1f') 'm']);
        elseif(iView==2 || iView==3)
            text(xMax+lExt,bld.flrT.z(i),[num2str(bld.flrT.iFlrArch(i),'%02i')]);
            text(xMin-lExt/5,bld.flrT.z(i),[num2str(bld.flrT.z(i),'%04.1f') 'm']);
        end
    end

    % 绘制测点层高度线
    for i = 1:bld.nLyr
        hP(3) = plot([xMin xMax],bld.lyrT.z(i).*[1 1],'-r');hold on;
        if (iView==1 || iView==4)
            text(xMin-lExt,bld.lyrT.z(i),[num2str(i,'%02i')],'color','red');
            text(xMax+lExt/5,bld.lyrT.z(i),[num2str(bld.lyrT.z(i),'%04.1f') 'm'],'color','red');
        elseif(iView==2 || iView==3)
            text(xMax+lExt,bld.lyrT.z(i),[num2str(i,'%02i')],'color','red');
            text(xMin-lExt/5,bld.lyrT.z(i),[num2str(bld.lyrT.z(i),'%04.1f') 'm'],'color','red');
        end
    end

    axis equal;
    axis off;
    if (iView==2 || iView==3)   % 对 X- 和 Y+ 面的X/Y轴进行水平翻转
        set(gca,'XDir','reverse')   % 反转X轴方向
    end
    if (iView==1 || iView==2)   
        xlabel(['X (m)']);
    elseif (iView==3 || iView==4)
        xlabel(['Y (m)']);
    end    
    ylabel(['Z (m)']);
    axis([xMin-lExt,xMax+lExt,0,yMax+lExt]);  % yMin-lExt
    set(gca,'FontName','微软雅黑','FontSize',fontSize,'position',posAx,'TitleHorizontalAlignment','right');  %设置图形占整个画面
    %     title([num2str(i) '-' num2str(j) '-' side],'FontAngle','italic','Color','b','FontSize',2*fontSize);
    multiLgdF(hP,{'建筑轮廓','楼层高度','测点层高度'},[0.16,0.16,0.65;0.93 0.91,0.91],10);
    set(gcf,'Units','centimeters','position',[1 1 fig.width fig.height]);
    
    print('-vector','-dsvg',      [bld.pathGeom 'figures/结构轮廓及楼层高度图' bound{1}.class]);
    print('-dpng','-image','-r300',[bld.pathGeom 'figures/结构轮廓及楼层高度图' bound{1}.class]);
end

% scaleCP  = 0.2; 
% lExt     = 20;    % 画风压系数图时向外扩展的距离
% lXSubFig = bld.lX(1,1)/2 + lExt;
% lYSubFig = bld.lY(1,1)/2 + lExt;
% lYFig = 30;
% 
% figure
% plot(bld.lXLyr,bld.lyrT.z,'ok');hold on;
% plot(bld.flrT.lX,bld.flrT.z,'xk');hold on;
% plot([bld.lX4Interp(2:end,1)' 0 0 bld.lX4Interp(end,1)],[bld.lX4Interp(2:end,2)' bld.lX4Interp(end,2) 0 0],'-+k','linewidth',2);hold on;
% plot(bld.lYLyr,bld.lyrT.z,'or');hold on;
% plot(bld.flrT.lY,bld.flrT.z,'xr');hold on;
% plot([bld.lY4Interp(2:end,1)' 0 0 bld.lY4Interp(end,1)],[bld.lY4Interp(2:end,2)' bld.lY4Interp(end,2) 0 0],'-+r','linewidth',2);hold on;grid on;
% set(gca,'DataAspectRatio',[1 1 1]);
% legend('测压点层X向宽度','楼层X向宽度','建筑X向截面尺寸','测压点层Y向宽度','楼层Y向宽度','建筑Y向截面尺寸','location','northoutside');
% xlabel('风荷载计算用建筑截面宽度 (m)');ylabel('高度');
% xlim([0 ceil(max([bld.lXLyr;bld.lYLyr])/50)*50]);ylim([0 ceil(max(bld.lyrT.z)/50)*50]);
% set(gcf,'Units','centimeters','position',[11 2 10 25]);
% print('-dsvg','-vector',[bld.pathGeom 'figures/00风荷载计算用建筑截面宽度示意图.emf']);
% saveas(gcf,[bld.pathGeom 'figures/00风荷载计算用建筑截面宽度示意图.fig']);

end
