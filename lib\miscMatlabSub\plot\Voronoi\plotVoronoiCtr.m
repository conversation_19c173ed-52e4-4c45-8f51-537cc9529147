function plotVoronoiCtr(data,bound,x_trib,y_trib,flgTapIn,iView,valueCMap)
% 绘制 Voronoi 分区，充色，补数
                                                                                                                                                                              

yShftText = 1;   % 绘图时，测点编号向下偏离测点符号的距离
fontSize1 = 5;
lineWidth = 0.5;
rAxisY    = 1;      % X轴相对于Y轴显示比例
posAx     = [0.03,0.03,0.94,0.94];   % axes的位置信息
scaleFig  = 0.15;  % 实际建筑的 1米 显示为图片中的 scaleFig 厘米

[ix, iy, ~, ~] = Dim2F(iView);

dGap = 0;
xMax = max(bound.outer(:,ix))+dGap;
xMin = min(bound.outer(:,ix))-dGap;
yMax = max(bound.outer(:,iy))+dGap;
yMin = min(bound.outer(:,iy))-dGap;

[xLength,yLength,xLim,yLim] = splitPlotSizeF(xMin,xMax,yMin,yMax,bound.subFig);
figSize = [1 2 xLength*scaleFig yLength*scaleFig];

if strcmp(data.name,'测点')  % 绘制测点图
    nData = size(data.x,1);
    for k = 1:bound.subFig(1)
        for j = 1:bound.subFig(2)
            figure
            text(sum(xLim{k,j})/2,sum(yLim{k,j})/2,[bound.class ' ' bound.name ' ' num2str(k) '-' num2str(j)],...
                'FontAngle','italic','Color','b','FontSize',3*fontSize1,'HorizontalAlignment','center');
            for i=1:nData     % Plot tributary areas with holes
                if any(isnan(x_trib{i}))&&flgTapIn(i)
                    [patch_f,patch_v] = poly2fv(x_trib{i},y_trib{i});
                    patch('Faces',patch_f,'Vertices',patch_v,'FaceColor',[1 1 1],'LineStyle','none','FaceAlpha',0.5); hold on;
                    ind = find(isnan(x_trib{i}));
                    patch(x_trib{i}(1:ind-1),y_trib{i}(1:ind-1),[1 1 1],'FaceColor','none','linewidth',lineWidth); hold on;
                    patch(x_trib{i}(ind+1:end),y_trib{i}(ind+1:end),[1 1 1],'FaceColor','none','linewidth',lineWidth); hold on;
                    % Plot tributary areas without holes
                elseif flgTapIn(i)
                    patch(x_trib{i},y_trib{i},[1 1 1],'linewidth',lineWidth,'FaceAlpha',0.5);
                end
            end
            hold on;
            plot([bound.outer(:,ix);bound.outer(1,ix)],[bound.outer(:,iy);bound.outer(1,iy)],'-','linewidth',2*lineWidth,'Color',[0.5 0.5 0.5]);hold on;  % 绘制云图轮廓
            if isfield(bound,'astR')  % 绘制轮廓辅助线
                for i = 1:length(bound.astR)
                    plot(bound.astR{i}(:,ix),bound.astR{i}(:,iy),'-k','linewidth',1*lineWidth);hold on;
                end
            end
            daspect([1 rAxisY 1]);
            axis([xLim{k,j} yLim{k,j}]); axis off;
            if (iView==2 || iView==3)   % 对 X- 和 Y+ 面的X/Y轴进行水平翻转
                set(gca,'XDir','reverse')   % 反转X轴方向
            end
            plot(data.x(flgTapIn),data.y(flgTapIn),'b+','MarkerSize',3,'MarkerFaceColor','b');hold on;
            text(data.x(flgTapIn),data.y(flgTapIn)-yShftText,data.value(flgTapIn),'FontSize',fontSize1,'HorizontalAlignment','center');
            
            set(gca,'FontName','Times New Roman','FontSize',fontSize1,'position',posAx);hold on;
            set(gcf,'Units','centimeters','position',figSize);
            print('-vector','-dsvg',[data.pathFig, data.name '泰森分布图' bound.class bound.name num2str(k) num2str(j)]);
            print('-dpng','-image','-r300',[data.pathFig data.name '泰森分布图' bound.class bound.name num2str(k) num2str(j) '.png']);
        end
    end

else %  Voronoi Regions Figure
    
    nData      = size(data.x,1);
    % valueLevel = min(data.value):data.dLvl:max(data.value);   
    cMap       = genCMapMatF(data.value,valueCMap);  

    for k = 1:bound.subFig(1)
        for j = 1:bound.subFig(2)
            figure
            text(sum(xLim{k,j})/2,sum(yLim{k,j})/2,[bound.class ' ' bound.name ' ' num2str(k) '-' num2str(j)],...
                'FontAngle','italic','Color','b','FontSize',3*fontSize1,'HorizontalAlignment','center');
            for i = 1:nData     % Plot tributary areas with holes
                if any(isnan(x_trib{i}))&&flgTapIn(i)
                    [patch_f,patch_v] = poly2fv(x_trib{i},y_trib{i});
                    patch('Faces',patch_f,'Vertices',patch_v,'FaceColor',cMap(i,:),'LineStyle','none','FaceAlpha',0.5); hold on;
                    ind = find(isnan(x_trib{i}));
                    patch(x_trib{i}(1:ind-1),y_trib{i}(1:ind-1),cMap(i,:),'FaceColor','none','linewidth',lineWidth); hold on;
                    patch(x_trib{i}(ind+1:end),y_trib{i}(ind+1:end),cMap(i,:),'FaceColor','none','linewidth',lineWidth); hold on;
                    % Plot tributary areas without holes
                elseif flgTapIn(i)
                    patch(x_trib{i},y_trib{i},cMap(i,:),'linewidth',lineWidth,'FaceAlpha',0.5);
                end
            end
            hold on;
            plot([bound.outer(:,ix);bound.outer(1,ix)],[bound.outer(:,iy);bound.outer(1,iy)],'-','linewidth',2*lineWidth,'Color',[0.5 0.5 0.5]);hold on;  % 绘制云图轮廓
            if isfield(bound,'astR')  % 绘制轮廓辅助线
                for i = 1:length(bound.astR)
                    plot(bound.astR{i}(:,ix),bound.astR{i}(:,iy),'-k','linewidth',1*lineWidth);hold on;
                end
            end
        
            daspect([1 rAxisY 1]);
            axis([xLim{k,j} yLim{k,j}]); axis off;
            if (iView==2 || iView==3)   % 对 X- 和 Y+ 面的X/Y轴进行水平翻转
                set(gca,'XDir','reverse')   % 反转X轴方向
            end
            text(data.x(flgTapIn),data.y(flgTapIn),num2str(data.value(flgTapIn),'%3.1f'),'FontSize',fontSize1,'HorizontalAlignment','center');
            
            set(gca,'FontName','Times New Roman','FontSize',fontSize1,'position',posAx);hold on;
            set(gcf,'Units','centimeters','position',figSize);
            print('-vector','-dsvg',[data.pathFig, data.name '泰森分布图' bound.class bound.name num2str(k) num2str(j)]);
            print('-dpng','-image','-r300',[data.pathFig data.name '泰森分布图' bound.class bound.name num2str(k) num2str(j) '.png']);
        end
    end

    % 绘制风压 legend，如果已经绘制过legend图像，就退出
    % hFigs = findobj('Type', 'figure');
    % for i = 1:length(hFigs)
    %     if strcmp(hFigs(i).Name,'legend')
    %         return
    %     end
    % end

end

end