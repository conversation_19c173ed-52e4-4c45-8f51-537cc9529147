function [phiNorm,varargout] = modeShapeNormF(varargin)
% 模态振型的归一化处理
% 有两种方式：
% 1 质量正则化振型
% 2 常用的最大响应归一化振型
% 画出Etabs和sap2000中常用的方向因子dF，判断振型属于哪一种自由度控制

phiTmp = varargin{1};
nMode = size(phiTmp,2);

if nargin == 2
    M = varargin{2};

    % for i = 1:nMode
    %     ratioM1(i) = sqrt(abs(phiTmp(:,i)'*M*phiTmp(:,i)));   %   原始模态质量 老算法
    % end

    ratioM1 = sqrt(sum((phiTmp.^2) .* diag(M), 1));   %   原始模态质量 新算法，结果一样

    phiNorm = phiTmp./ratioM1;             %  质量正则化振型，采用 ton m 为单位输出时，需要放大1000倍，原因未知

    for i = 1:nMode
        ratioM2(i) = phiNorm(:,i)'*M*phiNorm(:,i);
    end

    mDiag = diag(M);
    for i = 1:nMode
        dF(1,i) = mDiag(1:3:end)'*(phiNorm(1:3:end,i)).^2;   % direction factor in ETABS
        dF(2,i) = mDiag(2:3:end)'*(phiNorm(2:3:end,i)).^2;
        dF(3,i) = mDiag(3:3:end)'*(phiNorm(3:3:end,i)).^2;
    end

    varargout{1} = ratioM2;
    varargout{2} = dF;        
elseif nargin == 1
    for i = 1:nMode
        phiNorm(:,i) = phiTmp(:,i)./max(abs(phiTmp(:,i)));  % 振型简单的最大值归一化
    end
end

end

