function [phi,M,T,zFlr,xyCtrFlr,iTwr] = YJK2LMMF(bld,varargin)

% YJK2LumpedMassModelF

%--------------------------------------------------------------------------
% 由YJK模型文件提取结构楼层的 振型、质量、周期、圆频率等
% phi            -- 结构振型，排列方式为：从上到下，顶层-1层，分别是x、y方向的位移和沿z轴的旋转
% M              -- 结构质量，单位为 ton 和 ton-m2
% T              -- 结构周期，单位为s
% zFlr           -- 楼层高度，连体则包括所有楼的楼板高度，单位为 m
% xyCtrFlr       -- 楼层质心坐标，单位为 m， 刚（质/扭转中）心在 Etabs模型坐标系 中的坐标
% nFlrModeDspl   -- 振型从上向下哪一层开始显示，有时顶冠层刚度很小振型很不协调，就不再显示
% flgBldReadPlot -- 是否再次读取文件提取动力学特征，如之前处理过数据，设为0可节省重复读取数据的时间
%--------------------------------------------------------------------------

% example:
% [phi,M,T,zFlr,xyCtrFlr,iTwr] = YJK2LMMF(prj.nameBldAllCN{iBld},prj.nameBldAllENG{iBld},0);

switch nargin
    case 2
        flgBldReadPlot = varargin{1};
        nFlrModeDspl  = 1;
    case 3
        flgBldReadPlot = varargin{1};
        nFlrModeDspl  = varargin{2};
end

fileWZQ   = ['./' bld.iBldStr '/wzq.out'];
fileWMASS = ['./' bld.iBldStr '/wmass.out'];
fileFEA   = ['./' bld.iBldStr '/fea.dat'];
fileMCV   = ['./' bld.iBldStr '/MassCentreVibrationData.xlsx'];   % Mass Centre Vibration 质心振动

if flgBldReadPlot ~= 1
    disp(['不重新提取 ' bld.iBldStr '(' bld.nameCN ') YJK的结构动力学信息导出文件']);
    load([bld.pathStruDyn 'struDynYJK' bld.iBldStr],'phi','M','T','zFlr','xyCtrFlr','iTwr');      % 动力学特征保存
elseif flgBldReadPlot == 1
    for i = 1:bld.nTwr

        [xyCtrFlr, zFlr, mXFlr, iTwr] = readYjkMass(fileWMASS,i);   % 读取楼层质量，第一行数据为楼层最高层，数据维度[nFlr, 1]
    
        [mXFlrNode, mZFlrNode, M] = readYJKNodeMass(fileFEA,xyCtrFlr,i);  % 读取楼层单元node质量，并计算楼层转动惯量，第一行数据为楼层最高层

        plotYJKMFlrF(bld,mXFlrNode,mZFlrNode,mXFlr,zFlr,i);   % 绘制对比YJK楼层质量数据，检验合理性

        [T, phi] = readModeByYJKSeismicForceF(fileWZQ,fileMCV,mXFlrNode,mZFlrNode,M,bld,i);  % 读取YJK的 nMode阶模态的平动系数、扭转系数，数据维度[nMode, 1] 地震力，数据维度{nMode}[nFlr, 3] 

        omg     = 2*pi./T;    % 圆频率
        phiNorm1 = modeShapeNormF(phi);
        plotTallBldShapeF(phiNorm1,T,omg,10,bld,i);
    
        save([bld.pathStruDyn 'struDynYJK' bld.iBldStr 'Twr' num2str(i)],'phi','M','T','zFlr','xyCtrFlr','iTwr');
    end

    hP = plotModeShapeNormMassF(bld,zFlr,phi,iTwr,3);  % 绘制质量归一化振型



    if bld.nTwr == 1
        save([bld.pathStruDyn 'struDynYJK' bld.iBldStr],'phi','M','T','zFlr','xyCtrFlr','iTwr');      % 动力学特征保存
    elseif bld.nTwr > 1   % 合并多塔楼的动力学信息，直接合并并不行，需要统一做质量归一化 @杨军辉 
        files = cell(bld.nTwr, 1);
        for j = 1:bld.nTwr
            files{j} = [bld.pathStruDyn 'struDynYJK' bld.iBldStr 'Twr' num2str(j) '.mat'];
        end
    
        data = cell(bld.nTwr, 1);            % 预分配数据数组
        for i = 1:bld.nTwr
            data{i} = load(files{i});               % 读取数据文件
        end
                        
        % 初始化数据数组
        M_all = cell(length(data), 1);
        phi_all = cell(length(data), 1);
        T_all = cell(length(data), 1);
        zFlr_all = cell(length(data),1);
        xyCtrFlr_all = cell(length(data),1);
        iFlrAll_all = cell(length(data),1);

        % 提取每个文件的质量矩阵、振型和周期数据
        for i = 1:length(data)
            M_all{i} = data{i}.M;
            phi_all{i} = data{i}.phi;
            T_all{i} = data{i}.T;
            zFlr_all{i} = data{i}.zFlr;
            xyCtrFlr_all{i} = data{i}.xyCtrFlr;
        end
        
        % 合并数据（这里采用简单的拼接方法，根据实际需求可能需要调整）
        % 质量矩阵是对角矩阵，采用块对角拼接
        M_merged = blkdiag(M_all{:});

        % 根据实际情况选择振型合并方式
        row_counts = cellfun(@(x) size(x, 1), phi_all);
        if all(row_counts == row_counts(1))
            % 行数相同，按列拼接
            phi_merged = horzcat(phi_all{:});
        else
            % 行数不同，按列拼接
            phi_merged = vertcat(phi_all{:});
        end

        % 周期数据不用合并
        T_merged = T_all{1};
        
        % 楼层高度拼接
        zFlr_merged = vertcat(zFlr_all{:});
        
        % 刚度中心坐标拼接
        xyCtrFlr_merged = vertcat(xyCtrFlr_all{:});

        % 
        % iFlrAll_merged = horzcat(iFlrAll_all{:});
        iFlrAll_merged = [iFlrAll_all{:}];  % Simply concatenate the cell arrays
        
        % 构建合并数据结构
        merged_data = struct();
        % 添加合并后的数据
        merged_data.M = M_merged;
        merged_data.phi = phi_merged;
        merged_data.T = T_merged;
        merged_data.zFlr = zFlr_merged;
        merged_data.xyCtrFlr = xyCtrFlr_merged;

        % 添加合并信息
        merged_data.merge_info = struct('towers', {bld.nTwr}, 'merge_date', string(datetime('now')));

        M = merged_data.M;
        % phi = merged_data.phi;
        [phi,~,~] = modeShapeNormF(merged_data.phi, M);    % 对于连体结构，此式正确，需要重新对合并后的振型做质量归一化处理
        T = merged_data.T;
        zFlr = merged_data.zFlr;
        xyCtrFlr = merged_data.xyCtrFlr;

        save([bld.pathStruDyn 'struDynYJK' bld.iBldStr],'phi','M','T','zFlr','xyCtrFlr','iTwr');      % 动力学特征保存
    end

end

end

