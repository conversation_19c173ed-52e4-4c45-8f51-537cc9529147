% 物理风洞试验及风振分析主程序
% 量纲及单位 长度：米m | 时间：秒s | 质量：吨ton | 压强：千帕kPa | 力：千牛kN
clc;clear;close all;tic
currentFilePath = mfilename('fullpath'); 
% 添加路径
pathCode = fullfile('d:', 'codes', 'WindIRC');
pathLib = fullfile('D:','SynologyDrive','MATLAB','miscMatlabSub');
addpath(genpath(fullfile(pathCode,'src','classes')));
addpath(genpath(fullfile(pathCode, 'src','utils')));
addpath(genpath(fullfile(pathCode, 'lib')));
addpath(genpath(fullfile(pathLib)));
addpath(genpath(fullfile('D:\SynologyDrive\MATLAB\20WindResistanceDesign\')));
addpath(genpath(fullfile('D:\SynologyDrive\MATLAB\SubProgram\StructureDynamics\')));
addpath(genpath(fullfile('D:\SynologyDrive\MATLAB\CNWind\')));

%% -------------------- 项目信息 ---------------------------
fileTstInfo = '项目试验信息_新木新村2';
prj = prjWTTC(fileTstInfo);
plotRWDF(prj,[10 50],0);   % 绘制风向影响系数 图片，并写入试验结果文件
plotTCF(prj,'tcDir',1,'flagRun',0);
save .\mat\prj prj

%% -------------------- 测点信息 ---------------------------
tap = tapC(fileTstInfo);
save .\mat\tap tap
plotTap3DF(prj,tap,'dShft',[0 1.5 0],'fig3D',0);

%% -------------------- 建筑信息 --------------------
for iBld = 2%1:prj.nBld
    load('.\mat\tap.mat');

    bld = bldWIVC(iBld,fileTstInfo,'tap',tap,'flgBldDynRead',0,'flgPlotFlrArea',0); 
    save(['.\mat\bld' num2str(iBld,'%02i')],'bld');
    plotTap2TBF(bld,'dShft',1.5,'flagPlot',0);           % 绘制并校核 楼层测点布置、矢量、承载宽度
    plotGeomTBF(bld,'sizeFig',[12 20],'flagPlot',0);     % 绘制并校核 结构立面轮廓与楼层几何信息
    plotBoundF(bld,'showTap',1,'sizeFig',[0 25],'view',0);   % 绘制并校核 结构立面轮廓，为测点布置图做准备
    plotAccPos(bld,'flagPlot',0);
    close all;
end

%% -------------------- 试验信息 ---------------------------
for iTst = 1:prj.nTst            % 该试验的总批次数
    tst = testSyncPresC(iTst,fileTstInfo,'flagPRefProc',1);              % 建立当前试验类
    save(['.\mat\tst' num2str(iTst,'%02i')],'tst');
    txt2cPF(tst,'flagPlotPTapHist',0,'flagRun', 1);     % 读取原始txt文件处理成测点压力（系数）时程文件mat  bld.taps,bld.tapErr
end

tstMerged = tstObjMergeF(prj.nTst,'flagRun',1);         % 合并多个tst对象
save('.\mat\tstMerged','tstMerged');
tstMergedExampleF(tstMerged);                           % 展示合并结果

% 使用说明：
% 1. 相同属性直接使用：tstMerged.fs, tstMerged.dt 等
% 2. 不同标量属性用索引：tstMerged.iTst(i), tstMerged.pDynMeanAll(i) 等
% 3. 不同矩阵属性用三维索引：tstMerged.dtProto(:,:,i) 等
% 4. 字符串属性用cell索引：tstMerged.date{i} 等

testCpCmbF(tap,tst,'flagCrctErrTap',1,'flagRun', 1);     % 多次测量数据合并，处理内外压测点 坏点



for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld'); 
    plotcPLyrF(bld,tst,tap,'dShft',3,'flgPlot',1);    % 绘制并校核 测点层的风压系数矢量图和曲线
    close all;
end

%% -------------------- 围护结构风压处理 ---------------------------
locPresF(tap,tst,0);
tap.loadwkLocM;  % 将计算极值风压值存入tap对象中
% pPeakCode = -2*bld.loadT.w0(bld.loadT.RP==50)*muZF(bld.tc(1),bld.h)
for iView = 0 %[1 2 3 4]
    plotTapVoronoi2F(bld,tap,'iManu',         iView,'flagFig',1,'flagDXF',1,'flagDoc',1,'nColTableDoc',2);
    plotTapVoronoi2F(bld,tap,'wkLocMaxLvl',   iView,'flagFig',1,'flagDXF',1,'flagDoc',1,'nColTableDoc',2);
    plotTapVoronoi2F(bld,tap,'wkLocMinLvl',   iView,'flagFig',1,'flagDXF',1,'flagDoc',1,'nColTableDoc',2);
    plotTapVoronoi2F(bld,tap,'wkLocSumMinLvl',iView,'flagFig',1,'flagDXF',1,'flagDoc',1,'nColTableDoc',2);
    plotTapVoronoi2F(bld,tap,'wkLocSumMaxLvl',iView,'flagFig',1,'flagDXF',1,'flagDoc',1,'nColTableDoc',2);
end

%% -------------------- 风荷载时程计算 ---------------------------
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld'); 
    dispFileDateF(matFilePathBld);
    matFilePathTap = '.\mat\tap.mat'; %'./00Tap/cPSum000.mat';
    load(matFilePathTap,'tap'); 
    dispFileDateF(matFilePathTap);
    matFilePathTst = '.\mat\tst01.mat';
    load(matFilePathTst,'tst');
    dispFileDateF(matFilePathTst);

    calWFTBF(bld,tst,tap,'flagRun',0);   % 计算楼层三向风荷载
    plotCFTBF(bld,1);          % 汇总楼层风荷载系数并绘图展示
end

%% -------------------- 风振响应分析 ---------------------------
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld'); 
    dispFileDateF(matFilePathBld);
    dispFileDateF([bld.pathLoad 'F000.mat']);
    matFilePathTst = '.\mat\tst01.mat';
    load(matFilePathTst,'tst');
    dispFileDateF(matFilePathTst);

    % 计算风振响应 并 保存 
    bld.RPLoop      = [10 50];       % [10 50] | 本项目选择的荷载重现期：10年用于计算加速度，50年用于计算极值响应
    bld.flgAnlsLoop = [1 2];        % 时域/频域分析方法Flag 1 = 'Time'; 2 = 'Freq' 
    bld.flgDrctLoop = [0 1];        % 考虑风速风向折减Flag  0 = 不考虑，1 = 考虑

    if iBld == 1
        bld.coefAmp     = [1 1 1];    % 当等效风荷载（基底弯矩）小于规范值的80%时，用基底弯矩的比值放大风荷载时程重新计算等效风荷载载，大于80%时取1。
    elseif iBld == 2
        bld.coefAmp     = [1 1 1];  % 当等效风荷载（基底弯矩）小于规范值的80%时，用基底弯矩的比值放大风荷载时程重新计算等效风荷载载，大于80%时取1。
    end

    calRspMiscF(bld,tst,1);       % 计算结构风振响应

    % 风振响应后处理
    % -------- 输入参数 【以上需要修改】
    bld.RP     = 10;
    bld.flgDrct = 1;
    plotRspF(bld,1);
end

%% -------------------- 楼层舒适度 计算 绘图 --------------------
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld'); 
    dispFileDateF(matFilePathBld);

    bld.flgDrctLoop = [0 1];        % 考虑风速风向折减Flag  0 = 不考虑，1 = 考虑
    
    [bld.aVecPeakS, bld.aCtrPeakS] = calAccManFlrF(bld,'flgPlot',1);      % 依据时域响应计算加速度矢量极值响应
    save(['.\mat\bld' num2str(bld.iBld,'%02i')],'bld');
end

%% -------------------- 等效风荷载 计算 绘图 --------------------
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld'); 
    dispFileDateF(matFilePathBld);
    bld.RP = 50;

    % -------- 输入参数 【需要修改】
    bld.flgDrctLoop  = [0 1];
    iFlrEq           = 2;      % 等效目标的楼层，顶层为1，自上而下。当楼层顶部有装饰性冠层时采用，但其实取与冠层顶部等效差别不大
    % -------- 输入参数 【需要修改】
    
    for j = 1:length(bld.flgDrctLoop)
        bld.flgDrct = bld.flgDrctLoop(j);
        [fEqIWL]  = eswlIWLF(bld);
        tmpField = ['Drct' num2str(bld.flgDrct)];
        bld.fEqELRCS.(tmpField)    = eswlELRCF(bld,iFlrEq);
        bld.fEqELRCRecS.(tmpField) = findESWLRecF(bld.fEqELRCS.(tmpField),bld);
    end
    save(['.\mat\bld' num2str(bld.iBld,'%02i')],'bld');

    bld.flgDrct = 1;
    plotESWLBaseF(bld,bld.fEqELRCS.(['Drct' num2str(bld.flgDrct)]),fEqIWL,'flgIWLPlot',0,'flgPlot',1);
end

%% -------------------- 等效静风荷载与规范/YJK对比 -------------------- 
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld');
    dispFileDateF(matFilePathBld);
    bld.RP = 50;
    bld.flgDrct = 0;   % 是否考虑风速的风向折减，此处一般都是考虑的

    % -------- 输入参数 【需要修改】
    swMyCodeAYJK     = 0;   % 0 - YJK风荷载  1 - 自编规范程序风荷载
    swFMeanPlot      = 0;   % 是否显示平均风洞风荷载
    storyDriftLim    = 500; % 层间位移角的限值
    % -------- 输入参数 【需要修改】
    
    [bld.fMaxYJK,bld.fFlrYJKT] = readYJKESWLF([bld.pathBld 'wmass.out'],1:bld.nTwr,bld.flgCrsESWLYJK);

    for j = 1:length(bld.flgDrctLoop)
        tmpField = ['Drct' num2str(bld.flgDrct)];
        bld.fEqELRCRecS.(tmpField) = findESWLRecF(bld.fEqELRCS.(tmpField),bld);
    end

    bld.storyDrift = calStoryDriftF(bld,storyDriftLim,'flgPlot',0);
    bld = vsESWLF(bld,swFMeanPlot,swMyCodeAYJK);
    save(['.\mat\bld' num2str(bld.iBld,'%02i')],'bld');
end

%% -------------------- 风洞试验完整结果保存至excel文件 --------------------
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld');
    dispFileDateF(matFilePathBld);
    matFilePathTst = '.\mat\tst01.mat';
    load(matFilePathTst,'tst');
    dispFileDateF(matFilePathTst);

    bld.flgDrct = 0;   % 是否考虑风速的风向折减，此处一般都是考虑的

    saveWTT2ExlF(tst,bld,'flagRun',1); 
    saveESWL2YJKF(bld);
end

%% -------------------- 风洞试验结果保存至Word文件 --------------------
for iBld = 2%1:prj.nBld
    matFilePathBld = ['.\mat\bld' num2str(iBld,'%02i') '.mat'];
    load(matFilePathBld,'bld');
    dispFileDateF(matFilePathBld);
    matFilePathTst = '.\mat\tst01.mat';
    load(matFilePathTst,'tst');
    dispFileDateF(matFilePathTst);

    bld.flgDrct = 1;   % 是否考虑风速的风向折减，此处一般都是考虑的
    
    saveWTT2DocF(bld,tst);
end