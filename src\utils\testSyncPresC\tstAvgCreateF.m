function [tst] = tstAvgCreateF(nTst, varargin)
% 功能：
% 创建一个新的tst对象，以tst01为基础，将多个tst对象的关键压力值取平均
% 然后重新计算相关的缩尺比例属性
% 说明：
% 1. 以tst01为基础创建新的tst对象
% 2. 将pTotalMeanAll、pStcMeanAll、pDynMeanAll取四个对象的平均值
% 3. 使用modelScaleM函数重新计算相关的缩尺比例属性
% 4. 其他属性保持tst01的数值
%
% 输入参数:
%   nTst - 试验对象数量
%   Name-Value 参数:
%     'flagRun' - 是否执行创建
%                 0: 不执行创建，直接返回 (默认)
%                 1: 执行创建
%
% 输出参数:
%   tst - 新创建的平均tst对象
%
% 示例:
%   tst = tstAvgCreateF(4, 'flagRun', 1);      % 基于4个tst对象创建平均对象
%
% @lichaosz 20250610

% 解析输入参数
p = inputParser;
p.addRequired('nTst', @(x) isnumeric(x) && isscalar(x) && x > 0);
p.addParameter('flagRun', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.parse(nTst, varargin{:});

flagRun = p.Results.flagRun;

if flagRun == 0
    disp('不执行tst平均对象创建');
    tst = [];
    return;
end

disp(['开始创建基于 ' num2str(nTst) ' 个tst对象的平均对象...']);

% 读取所有tst对象
tstObjects = cell(nTst, 1);
for i = 1:nTst
    matFile = ['.\mat\tst' num2str(i,'%02i') '.mat'];
    if exist(matFile, 'file') == 2
        tstData = load(matFile, 'tst');
        tstObjects{i} = tstData.tst;
        disp(['已读取 ' matFile]);
    else
        error(['文件 ' matFile ' 不存在']);
    end
end

% 以tst01为基础创建新对象
tst = tstObjects{1};
disp('以tst01为基础创建新对象');

% 收集需要平均的压力值
pTotalMeanAll_values = zeros(nTst, 1);
pStcMeanAll_values = zeros(nTst, 1);
pDynMeanAll_values = zeros(nTst, 1);

for i = 1:nTst
    pTotalMeanAll_values(i) = tstObjects{i}.pTotalMeanAll;
    pStcMeanAll_values(i) = tstObjects{i}.pStcMeanAll;
    pDynMeanAll_values(i) = tstObjects{i}.pDynMeanAll;
end

% 计算平均值
tst.pTotalMeanAll = mean(pTotalMeanAll_values);
tst.pStcMeanAll = mean(pStcMeanAll_values);
tst.pDynMeanAll = mean(pDynMeanAll_values);

disp('压力值平均计算完成:');
disp(['  pTotalMeanAll: ' num2str(pTotalMeanAll_values', '%.4f ') ' → ' num2str(tst.pTotalMeanAll, '%.4f') ' Pa']);
disp(['  pStcMeanAll:   ' num2str(pStcMeanAll_values', '%.4f ') ' → ' num2str(tst.pStcMeanAll, '%.4f') ' Pa']);
disp(['  pDynMeanAll:   ' num2str(pDynMeanAll_values', '%.4f ') ' → ' num2str(tst.pDynMeanAll, '%.4f') ' Pa']);

% 重新计算模型缩尺比例
disp('重新计算模型缩尺比例...');
try
    tst = tst.modelScaleM();
    disp('模型缩尺比例计算完成');

    % 显示重新计算的关键参数
    disp('重新计算的参数:');
    if ~isempty(tst.uRefModel)
        if isnumeric(tst.uRefModel) && isscalar(tst.uRefModel)
            disp(['  uRefModel: ' num2str(tst.uRefModel, '%.4f') ' m/s']);
        elseif isnumeric(tst.uRefModel) && isvector(tst.uRefModel)
            disp(['  uRefModel: ' num2str(tst.uRefModel(1), '%.4f') ' m/s (第1风向)']);
        end
    end

    if ~isempty(tst.tRatio)
        if isnumeric(tst.tRatio) && ismatrix(tst.tRatio)
            disp(['  tRatio (第1风向,50年): ' num2str(tst.tRatio(1,3), '%.6f')]);
        end
    end

    if ~isempty(tst.fRatio)
        if isnumeric(tst.fRatio) && ismatrix(tst.fRatio)
            disp(['  fRatio (第1风向,50年): ' num2str(tst.fRatio(1,3), '%.6f')]);
        end
    end

catch ME
    warning(['模型缩尺比例计算失败: ' ME.message]);
    disp('保持原有的缩尺比例参数');
end

end
