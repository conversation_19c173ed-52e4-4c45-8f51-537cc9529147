function [tstMerged] = tstObjMergeF(nTst, varargin)
% 功能：
% 将多个tst对象合并为一个对象，相同属性保留一份，不同属性按序号依次保存
% 矩阵属性增加行或列，复杂属性用cell数组保存
%
% 输入参数:
%   nTst - 试验对象数量
%   Name-Value 参数:
%     'flagRun' - 是否执行合并
%                 0: 不执行合并，直接返回 (默认)
%                 1: 执行合并
%
% 输出参数:
%   tstMerged - 合并后的tst对象结构体
%
% 示例:
%   tstMerged = tstObjMergeF(4);                    % 合并4个tst对象，使用默认参数
%   tstMerged = tstObjMergeF(4, 'flagRun', 1);      % 强制执行合并
%
% @lichaosz 20241220

% 解析输入参数
p = inputParser;
p.addRequired('nTst', @(x) isnumeric(x) && isscalar(x) && x > 0);
p.addParameter('flagRun', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.parse(nTst, varargin{:});

flagRun = p.Results.flagRun;

if flagRun == 0
    disp('不执行tst对象合并');
    tstMerged = [];
    return;
end

disp(['开始合并 ' num2str(nTst) ' 个tst对象...']);

% 初始化
tstObjects = cell(nTst, 1);
tstMerged = struct();

% 读取所有tst对象
for i = 1:nTst
    matFile = ['.\mat\tst' num2str(i,'%02i') '.mat'];
    if exist(matFile, 'file') == 2
        tstData = load(matFile, 'tst');
        tstObjects{i} = tstData.tst;
        disp(['已读取 ' matFile]);
    else
        error(['文件 ' matFile ' 不存在']);
    end
end

% 获取第一个对象的所有属性名
firstTst = tstObjects{1};
propNames = properties(firstTst);

disp(['共发现 ' num2str(length(propNames)) ' 个属性需要处理']);

% 遍历每个属性进行合并
for iProp = 1:length(propNames)
    propName = propNames{iProp};

    try
        % 收集所有对象中该属性的值
        propValues = cell(nTst, 1);
        for i = 1:nTst
            propValues{i} = tstObjects{i}.(propName);
        end

        % 判断属性是否相同
        isIdentical = true;
        firstValue = propValues{1};

        for i = 2:nTst
            if ~isequal(firstValue, propValues{i})
                isIdentical = false;
                break;
            end
        end

        if isIdentical
            % 属性相同，直接保留第一个值
            tstMerged.(propName) = firstValue;
            disp(['属性 ' propName ': 相同，保留原值']);
        else
            % 属性不同，需要合并
            try
                tstMerged.(propName) = mergeProperty(propName, propValues);
                disp(['属性 ' propName ': 不同，已合并']);
            catch ME
                % 如果合并失败，使用cell数组保存原始值
                tstMerged.(propName) = propValues;
                disp(['属性 ' propName ': 合并失败，使用cell数组保存 - ' ME.message]);
            end
        end
    catch ME
        % 如果整个属性处理失败，跳过该属性
        disp(['属性 ' propName ': 处理失败，跳过 - ' ME.message]);
        continue;
    end
end

% 添加合并信息
tstMerged.mergeInfo = struct();
tstMerged.mergeInfo.nTstMerged = nTst;
tstMerged.mergeInfo.mergeDate = char(datetime("now", 'Format', 'yyyy-MM-dd HH:mm:ss'));
tstMerged.mergeInfo.originalFiles = cell(nTst, 1);
for i = 1:nTst
    tstMerged.mergeInfo.originalFiles{i} = ['tst' num2str(i,'%02i') '.mat'];
end

disp(['tst对象合并完成，共合并了 ' num2str(nTst) ' 个对象']);

end

function mergedValue = mergeProperty(propName, propValues)
% 合并单个属性的函数
% 根据属性类型采用不同的合并策略

nTst = length(propValues);
firstValue = propValues{1};

try
    % 判断数据类型
    if isnumeric(firstValue)
        if isscalar(firstValue)
            % 标量数值：转换为行向量
            mergedValue = zeros(1, nTst);
            for i = 1:nTst
                if isnumeric(propValues{i}) && isscalar(propValues{i})
                    mergedValue(i) = propValues{i};
                else
                    % 如果不是标量，回退到cell数组
                    mergedValue = propValues;
                    return;
                end
            end
        elseif isvector(firstValue)
            % 向量：检查所有向量长度是否相同
            firstLength = length(firstValue);
            canMerge = true;
            for i = 2:nTst
                if ~isvector(propValues{i}) || length(propValues{i}) ~= firstLength
                    canMerge = false;
                    break;
                end
            end

            if canMerge
                % 按行合并向量
                mergedValue = zeros(nTst, firstLength);
                for i = 1:nTst
                    if isrow(propValues{i})
                        mergedValue(i, :) = propValues{i};
                    else
                        mergedValue(i, :) = propValues{i}';
                    end
                end
            else
                mergedValue = propValues;
            end
        elseif ismatrix(firstValue)
            % 矩阵：检查前两维是否相同
            [rows, cols] = size(firstValue);
            canMerge = true;
            for i = 2:nTst
                [r, c] = size(propValues{i});
                if r ~= rows || c ~= cols
                    canMerge = false;
                    break;
                end
            end

            if canMerge
                mergedValue = zeros(rows, cols, nTst);
                for i = 1:nTst
                    mergedValue(:, :, i) = propValues{i};
                end
            else
                % 维度不匹配，使用cell数组
                mergedValue = propValues;
            end
        else
            % 其他数值类型，使用cell数组
            mergedValue = propValues;
        end
    elseif ischar(firstValue) || isstring(firstValue)
        % 字符串：使用cell数组
        mergedValue = propValues;
    elseif iscell(firstValue)
        % cell数组：嵌套在更大的cell数组中
        mergedValue = propValues;
    elseif isstruct(firstValue)
        % 结构体：使用cell数组
        mergedValue = propValues;
    elseif islogical(firstValue)
        % 逻辑值
        if isscalar(firstValue)
            mergedValue = false(1, nTst);
            for i = 1:nTst
                if islogical(propValues{i}) && isscalar(propValues{i})
                    mergedValue(i) = propValues{i};
                else
                    mergedValue = propValues;
                    return;
                end
            end
        else
            mergedValue = propValues;
        end
    else
        % 其他类型：使用cell数组
        mergedValue = propValues;
    end
catch ME
    % 如果出现任何错误，回退到cell数组
    warning(['属性 ' propName ' 合并失败，使用cell数组: ' ME.message]);
    mergedValue = propValues;
end

end
