function [ ] = testCpCmbF(tap, tst, varargin)
% 功能：
% 1 将同一试验项目中多次测试记录的cP数据合并为单一文件
% 2 剔除未连接测点的空置通道，并将cP中通道的排序调整为tapC中测点的排序，使cP的行号即为tapsT中的iTap (也即tapsT按从小到大排序后的行号）
% 3 修复损坏测点的数据
% 4 保存风压时程数据cP，
%   a. 直接保存为cp000.mat：用于双面围护结构的极值风压计算（如屋盖包括屋面和吊顶时，高层女儿墙为双面幕墙等）
%   b. 合并双面测点风压后保存为cPSum000.mat：1、主体结构风荷载计算;2、围护结构为单层，需要合并计算极值风压
%
% 输入参数:
%   tap - 测点对象
%   tst - 测试对象
%   Name-Value 参数:
%     'flagRun' - 是否重新合并测点风压cP
%                 0: 不重新合并 (默认)
%                 1: 重新合并
%
% 示例:
%   testCpCmbF(tap, tst);                    % 使用默认参数，不重新合并
%   testCpCmbF(tap, tst, 'flagRun', 0);      % 不重新合并
%   testCpCmbF(tap, tst, 'flagRun', 1);      % 重新合并
%
% 说明：
% 此程序非常关键，直接影响后续主体和围护结构风荷载的计算
%
% @lichaosz 20230819

% 解析输入参数
p = inputParser;
p.addParameter('flagRun', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.addParameter('flagCrctErrTap', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.parse(varargin{:});
flagRun = p.Results.flagRun;
flagCrctErrTap = p.Results.flagCrctErrTap;

if flagRun == 0
    disp('不重新合并 测点风压cP');
    return;
end

tp      = tap.t;
dgrChar = tst.dgrChar;
nTst    = tst.nTst;
tstBlockSNT = table();
for k = 1:nTst
    tstS = load(['.\mat\tst' num2str(k,'%02i')],'tst');
    tstCur = tstS.tst;  % 使用临时变量存储tst结构体
    iTstM = repmat(k,length(tstCur.iBlockSlct),1);
    tstBlockSN = [iTstM,tstCur.iBlockSlct',tstCur.iBatch',tstCur.iBlockDsgn'];
    tmpT = array2table(tstBlockSN, 'VariableNames', {'iTst','iBlockSlct','iBatch','iBlockDsgn'});
    tstBlockSNT = [tstBlockSNT; tmpT];
end

for i = 1:max(unique(tstBlockSNT.iBatch))
    nBlockBtach(i) = nnz(tstBlockSNT.iBatch == i);
end

parfor i = tst.iDgrProc   % parfor

    cPO = zeros(tst.nMinSamp,height(tstBlockSNT)*tst.nChnlPer);   % 完整的风压系数时程矩阵

    for k = 1:nTst
        tstS = load(['.\mat\tst' num2str(k,'%02i')],'tst');
        tstCur = tstS.tst;  % 使用临时变量存储tst结构体
        cPS  = load([tstCur.pathPMat 'cP' dgrChar{i}],'cP');
        cPi  = cPS.cP;
    
        for j = 1:length(tstCur.iBlockDsgn)
            iTmp = (tstCur.iBatch(j)-1)*sum(nBlockBtach(1:tstCur.iBatch(j)-1));
            cPO(:,tstCur.nChnlPer*(tstCur.iBlockDsgn(j)-1+iTmp)+1:tstCur.nChnlPer*(tstCur.iBlockDsgn(j)+iTmp)) = cPi(:,(tstCur.nChnlPer*(j-1)+1:tstCur.nChnlPer*j));
        end
    end

    %  ---------- 坏 点 修 正 ----------
    if flagCrctErrTap == 1
        [cPO] = pCrctErrTapF(cPO,tap,i); 
    else
        if i == 1
            disp('坏点未修正');
        end
    end
    %  ---------- 坏 点 修 正 ----------
    
    %  处理所有测点的风压系数时程
    cP = cPO(:,tp.iChnlDatTot);     % 去除未连接通道的数据，并将cP中通道的排序调整为tapC中测点的排序。
    parSaveF([tap.path 'cP' dgrChar{i}], cP, 'cP');

    % 将双面测点的风压合并，仅保留数据中外表面测点风压
    if max(tp.iViewInt) > 0     
        iTapWithInt = tp.iViewInt ~= 0;    % 有 内压参考测点 的外压测点
        cPO(:,tp.iChnlDatTot(iTapWithInt)) = cPO(:,tp.iChnlDatTot(iTapWithInt)); % - cPO(:,tp.iBackChnlDatTot(iTapWithInt));
        cP = cPO;   %(:,tp.iChnlDatTot(tap.idxMain));   % 合并双面测点后的用于计算主体结构荷载的测点
        parSaveF([tap.path 'cPSum' dgrChar{i}], cP, 'cP');
    else
        parSaveF([tap.path 'cPSum' dgrChar{i}], cP, 'cP');
    end

    disp([dgrChar{i} ' 1 cP in multi-test is combined. 2 Errors are corrected. 3 cP have internal referrence tap are sumed.']);

end

end