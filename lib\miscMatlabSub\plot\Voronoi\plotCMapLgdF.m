
function valueCMap = plotCMapLgdF(data,bound,valueData,dLvl)
% 将dataValue的数值转化为对应的颜色标签值，用于云图对应的填充颜色，并绘制ColorMap 的 legend图像

fontSize1 = 5;

valueLevel = min(valueData):dLvl:max(valueData);   %最大正风压的数值范围，以data.dLvl=0.5为间隔
nLvlLgd    = length(valueLevel);  % 数值范围图例数量
cMapLvl    = parula(nLvlLgd);  %产生数量为nLvlLgd的jet颜色形式
valueCMap  = [valueLevel',cMapLvl];

% 绘制ColorMap legend 图像
hFig2  = figure; hFig2.Name = 'legend';
width  = 0.1;  %图例的宽度
height = 0.25;  %图例的高度
Position_x=0;
Position_y=0;
for i=1:nLvlLgd
    rectangle('Position',[Position_x,Position_y+height*(i-1),width,height],'facecolor',cMapLvl(i,:))  %图例矩形图
    hold on
end
axis([-inf,inf,0,height*nLvlLgd])  %限制Y轴显示范围
set(gca,'xtick',[]); %关闭x轴
set(gca,'YTick',(Position_y+0.5*height):height:(Position_y+(nLvlLgd)*height));
set(gca,'YTicklabel',valueLevel);
ylabel(data.name,'FontName','宋体','FontSize',2*fontSize1);
set(gca,'position',[0.6,0.05,0.2,0.9],'FontSize',2*fontSize1);
set(gcf,'Units','centimeters','position',[1 1 3 5]);
print('-vector','-dsvg',[data.pathFig, data.name '泰森分布图' bound.class '图例']);
print('-dpng','-image','-r300',[data.pathFig data.name '泰森分布图' bound.class '图例.png']);

end