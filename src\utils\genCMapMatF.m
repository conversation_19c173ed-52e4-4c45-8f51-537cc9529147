function [c_map] = genCMapMatF(dataValue,valueCMap)
% 为当前数值矩阵dataValue，在预先指定完整colorMap及数值分级框架中生成对应的颜色标签值
% 用于填充云图对应的颜色
% valueCMap来自于更大（完整）数据的分级值及对应的cMap
%
% 20250602 lichaosz

valueLvl = valueCMap(:,1);
cMapLvl  = valueCMap(:,2:4);

c_map = zeros(length(dataValue),3);
for i=1:length(valueLvl)
    id = find(dataValue==valueLvl(i));
    if ~isempty(id)
        c_map(id,:)=repmat(cMapLvl(i,:),length(id),1);
    end
end
end

