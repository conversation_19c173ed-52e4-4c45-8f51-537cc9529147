function rpt = img2doc(img,doc,varargin)
% 将指定图片写入word文档
% 功能
% png等位图图片支持根据列数调整图片大小
% 矢量图图片需要预先指定图片宽度 widthImg
%
% img       —— 待写入图片的信息
% img.path  —— 待写入图片路径
% img.name  —— 待写入图片文件名
% img.title —— 待写入图片在doc中的图名
% nCol      —— 图片表格的列数，自动将图片宽度等比例缩放为 最大宽度/nCol
% doc.name  —— word文档的文件名
% doc.path  —— word文档存放的路径

% 用法1：绘制完图片关闭rpt
% img{m}.path  = [bld.pathGeom 'figures\'];
% img{m}.name  = ['结构轮廓分区绘制示意图11' bound{i}.class '.png'];
% img{m}.title = [bound{i}.class '面上的结构轮廓分区绘制示意图'];
% doc.name = [data{i}.name '泰森分布图' bound{i}.class];
% img2doc(img,doc,'nCol',2);
% 
% 用法2：绘制完图片还需要再增加一些内容
%     doc.name = [nameEN '多风向基底剪力弯矩' 'Drct' num2str(flgDrct)];
%     rpt = img2doc(img,doc,'nCol',2,'widthImg',7.5,'flgRptClose',0);
%     append(rpt,'test');
%     close(rpt);
% 
% @lichaosz 20230611

import mlreportgen.dom.*
import mlreportgen.report.*

nColDefault        = 1;     % 默认 1 列表格放置图片
widthImgDefault    = 100;   % 默认值，此值非100后即需将图片修改至指定宽度
flgRptCloseDefault = 1;     % 默认关闭打开的rpt文档

p = inputParser;
p.addRequired('img', @iscell);
p.addRequired('doc', @isstruct);

validScalarPosNum = @(x) isnumeric(x) && isscalar(x) && (x >= 0);
p.addParameter('nCol',nColDefault,validScalarPosNum);
p.addParameter('widthImg',widthImgDefault,validScalarPosNum);
p.addParameter('flgRptClose',flgRptCloseDefault,validScalarPosNum);
checkReport = @(x) isa(x, 'Report');  % 定义Report验证函数
p.addParameter('rpt',checkReport);

p.parse(img,doc,varargin{:});

nCol        = p.Results.nCol;
widthImg    = p.Results.widthImg;
flgRptClose = p.Results.flgRptClose;
rpt         = p.Results.rpt;

figWidthLimDoc = 16/nCol;  % word 中A4除去2.5左右边距，则图片的宽度最大值为 21-5=16，此处取 16 cm
DPIImg = 300;

if isempty(rpt)
    pathNameDoc = [doc.path doc.name];
    if exist([img{1}.path '\~$' doc.name],'file')==2
        warning(['文件 ' doc.name ' 被程序打开，无法写入，请关闭']);
        return;
    end
    
    if exist(pathNameDoc,'file')==2
        delete(pathNameDoc);
    end
    rpt = Report(pathNameDoc, 'docx','d:\SynologyDrive\Myfiles\96 常用备份\microsoftOffice\Templates\word\matlab保存高保真图片A4模板.dotx');
end

fontFamily = FontFamily('Times New Roman');
% fontFamily.BackupFamilyNames = {'Arial'};
fontFamily.EastAsiaFamilyName = '宋体';
fontSize = FontSize('10.5pt');

t = Table();      % 初始化表格作为开始，后续逐渐加入内容
for i = 1:length(img)
    img{i}.img = Image([img{i}.path img{i}.name]);
%     img{i}.ScaleToFit = true; % 自适应缩放图片以适应单元格
    if widthImg ~= 100
        img{i}.img.Style = {ScaleToFit, Width([num2str(widthImg) 'cm'])};
    else
        infoImg = imfinfo([img{i}.path img{i}.name]);
        if infoImg.Width / DPIImg * 2.54 > figWidthLimDoc
            img{i}.img.Style = {ScaleToFit,Width([num2str(figWidthLimDoc,'%.2f') 'cm'])};
        else
            img{i}.img.Style = {ScaleToFit,Width([num2str(infoImg.Width / DPIImg * 2.54,'%.2f') ' cm'])};
        end
    end
end

k = 0;
for i = 1:ceil(length(img)/nCol)
    tr1 = TableRow();   % 一行图片
    tr2 = TableRow();   % 一行图名
    if mod(length(img),nCol) ~= 0 && i == 1
        k = k + 1;
        append(tr1,TableEntry(img{k}.img));    % 一行图片
        append(tr2,TableEntry(img{k}.title));  % 一行图名
    else
        for j = 1:nCol
            k = k + 1;
            append(tr1,TableEntry(img{k}.img));    % 一行图片
            append(tr2,TableEntry(img{k}.title));  % 一行图名
        end
    end
    append(t,tr1);
    append(t,tr2);
end

t.Border = 'none';
t.OuterLeftMargin = '0cm';
t.TableEntriesInnerMargin = '0cm';
t.TableEntriesStyle = {fontSize};
t.Style = {fontFamily};
t.TableEntriesHAlign = 'center';
t.TableEntriesVAlign = 'middle';

add(rpt, t);
append(rpt,PageBreak());

if flgRptClose == 1
    close(rpt);
    rptview(rpt);
end

end