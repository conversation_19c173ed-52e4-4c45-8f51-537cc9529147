function [xTrib,yTrib,idxTapIn] = VoronoiBoundF(data,bound,iView)
% 计算结点信息data 在 边界bound 中的Voronoi多边形信息，仅适用于二维

% data.x      -- 
% data.y      -- 所有监测点坐标矩阵，二维，

% bound.outer -- 设置监测点的实际画云图轮廓
% bound.inner -- 云图的内部边界
% bound.ast   -- 轮廓的辅助线段（包含未测监测点部分）
   
% 输出泰森多边形的关键信息

[ix, iy, ~, ~] = Dim2F(iView);

xBound = bound.outer(:,ix);  %面的轮廓横坐标
yBound = bound.outer(:,iy);  %面的轮廓纵坐标

if ~isempty(bound.inner)
    for j = 1:length(bound.inner)
        xInn = bound.inner{j}(:,ix);  %面的轮廓横坐标
        yInn = bound.inner{j}(:,iy);  %面的轮廓纵坐标
        
            % Counter-clockwise interior boundary (vertices)
        [xInnCcw,yInnCcw]=poly2ccw(xInn,yInn);
        
        xBound=[xBound;nan;xInnCcw];
        yBound=[yBound;nan;yInnCcw];
    end
end

[in,on]  = inpolygon(data.x,data.y,xBound,yBound);  %判别筛选的的监测点，是否在轮廓线里边或者轮廓线上
idxTapIn = in==1 | on==1;   %判别所有监测点是否在轮廓线上或里边的矩阵，0表示在轮廓线外边，1表示在轮廓线上或里边
P_ind    = find(idxTapIn);   %找出在轮廓线上或里边的监测点的编号
P_in     = [data.x(P_ind,:) data.y(P_ind,:)];    %对应找出在轮廓线上或里边的监测点的坐标

% Built-in MATLAB function
warning('off','MATLAB:triangulation:EmptyTri2DWarnId');
DT=delaunayTriangulation(P_in);

% Ensure that Delaunay triangulation was successful
if isempty(DT.ConnectivityList)~=1
    
    % Built-in MATLAB functions
    [V,R]=voronoiDiagram(DT);
    [vx,vy]=voronoi(DT);
    vx=vx.';
    vy=vy.';
    
    % Eliminate zero-length vectors
    vd=sqrt((vx(:,2)-vx(:,1)).^2+(vy(:,2)-vy(:,1)).^2);
    vx(vd==0,:)=[];
    vy(vd==0,:)=[];
    
    % Find unique values in vx (and vy) to replace "infinite vertices" %
    % 无穷远的节点仅使用一次，其他节点是不同多边形共用，出现多次？
    [u,~,indu]=unique([vx(:),vy(:)],'rows');
    uu=u(accumarray(indu,1)==1,:);
    ux=uu(:,1);
    uy=uu(:,2);
    
    % Create infinite vectors to help bound infinite regions
    kx=zeros(length(ux),2);
    ky=zeros(length(uy),2);
    
    for ii=1:length(ux)
        [induu]=find(vx==ux(ii)&vy==uy(ii));
        [indi,~]=ind2sub(size(vx),induu);
        kx(ii,:)=[vx(indi,1);vx(indi,2)].';
        ky(ii,:)=[vy(indi,1);vy(indi,2)].';
    end
    
    % Extend the infinite line segments
    kd=sqrt((kx(:,2)-kx(:,1)).^2+(ky(:,2)-ky(:,1)).^2);
    kextend=max(abs([xBound;yBound]))*3;
    kx(:,2)=kx(:,1)+(kx(:,2)-kx(:,1))./kd*kextend;
    ky(:,2)=ky(:,1)+(ky(:,2)-ky(:,1))./kd*kextend;
    
    % Sort the infinite line segments in counter-clockwise order
    xmid=(max(P_in(:,1))-min(P_in(:,1)))/2;
    ymid=(max(P_in(:,2))-min(P_in(:,2)))/2;
    [~,kbound]=sort(atan2(ky(:,2)-ymid,kx(:,2)-xmid));
    kbound(end+1)=kbound(1);
    
    % Initialize variables
    x_trib_in=cell(length(R),1);
    y_trib_in=cell(length(R),1);
    R_new=cell(length(R),1);
    V_new=V;
    
    % Loop over all tap tributary areas
    for ii=1:size(R,1)
        % Eliminate repeated vertices
        [~,IA,~]=unique([V(R{ii},1),V(R{ii},2)],'rows','stable');
        R{ii}=R{ii}(IA);
        
        % Fix regions with infinite vertices
        if any(R{ii}==1)
            % Look for infinite vectors that begin closest to each end of unbounded region
            v1_ind=knnsearch([kx(:,1),ky(:,1)],[V(R{ii}(2),1),V(R{ii}(2),2)],'Distance','euclidean','k',2);
            v2_ind=knnsearch([kx(:,1),ky(:,1)],[V(R{ii}(end),1),V(R{ii}(end),2)],'Distance','euclidean','k',2);
            
            flag1=kx(v1_ind(1),1)==kx(v1_ind(2),1)&&ky(v1_ind(1),1)==ky(v1_ind(2),1);
            flag2=kx(v2_ind(1),1)==kx(v2_ind(2),1)&&ky(v2_ind(1),1)==ky(v2_ind(2),1);
            
            % Method 1: use if infinite vectors begin in same location
            if flag1==1||flag2==1
                for jj=1:length(kbound)-1
                    % Trial vertices
                    v1=kbound(jj);
                    v2=kbound(jj+1);
                    Vx=[kx(v2,2);kx(v2,1);V(R{ii}(3:end-1),1);kx(v1,1);kx(v1,2)];
                    Vy=[ky(v2,2);ky(v2,1);V(R{ii}(3:end-1),2);ky(v1,1);ky(v1,2)];
                    
                    % Update V if trial vertices enclose tap
                    if inpolygon(P_in(ii,1),P_in(ii,2),Vx,Vy)==1
                        lv=size(V,1);
                        V_new(lv+1,:)=[Vx(1),Vy(1)];
                        V_new(lv+2,:)=[Vx(end),Vy(end)];
                        R_new{ii}=[lv+1,R{ii}(2:end),lv+2];
                    end
                    
                end
                
                % Method 2: (quicker) use if infinite vectors begin in unique locations
            else
                % Update V using infinite vectors
                lv=size(V,1);
                V_new(lv+1,:)=[kx(v1_ind(1),2),ky(v1_ind(1),2)];
                V_new(lv+2,:)=[kx(v2_ind(1),2),ky(v2_ind(1),2)];
                R_new{ii}=[lv+1,R{ii}(2:end),lv+2];
            end
            
        else
            R_new{ii}=R{ii};
        end
        
        % Make voronoi regions clockwise
        [xcw,ycw]=poly2cw(V_new(R_new{ii},1),V_new(R_new{ii},2));
        
        % Calculate the portion of tributary area bound within limits
%         [x_trib_temp,y_trib_temp]=polybool('intersection',xcw,ycw,xBound,yBound);
        A = polyshape(xcw,ycw,'Simplify',false); 
        B = polyshape(xBound,yBound,'Simplify',false); 
        C = intersect(A,B);
        [x_trib_temp,y_trib_temp] = boundary(C);
        
        x_trib_in{ii}=x_trib_temp;
        y_trib_in{ii}=y_trib_temp;
    end
    
    % If Delaunay triangulation was unsuccessful (e.g., due to collinear points), add four bounding points
else
    
    % Append list of points with four new bounding points
    Pextend=max(abs([xBound;yBound]))*10;
    P_add=[P_in;[1 1; -1 1; -1 -1; 1 -1]*Pextend];
    
    % Built-in MATLAB functions
    DT=delaunayTriangulation(P_add);
    [V,R]=voronoiDiagram(DT);
    
    % Remove Voronoi regions created by additional points
    R_new=R(1:size(P_in,1));
    V_new=V;
    
    % Initialize variables
    x_trib_in=cell(length(R_new),1);
    y_trib_in=cell(length(R_new),1);
    
    % Loop over all tap tributary areas
    for ii=1:size(R_new,1)
        % Make Voronoi regions clockwise
        [xcw,ycw]=poly2cw(V_new(R_new{ii},1),V_new(R_new{ii},2));
        
        % Calculate the portion of tributary area bound within limits
        [x_trib_temp,y_trib_temp]=polybool('intersection',xcw,ycw,xBound,yBound);
        x_trib_in{ii}=x_trib_temp;
        y_trib_in{ii}=y_trib_temp;
    end
    
end

% Initialize variables
xTrib=cell(size(data.x,1),1);
yTrib=cell(size(data.x,1),1);

% Expand to all taps (including those outside bounds)
for ii=1:length(P_ind)
    xTrib{P_ind(ii)} = x_trib_in{ii};
    yTrib{P_ind(ii)} = y_trib_in{ii};
end

end