function [] = plotTap2TBF(bld,varargin)
%
% 功能： 
% 绘制测点的2D楼层平面平面图，包含测点位置，区分内外测点，方向矢量，测点承载宽度
%
% 用途： 
% 用以检查测点位置、矢量方向、承载宽度的正确性
%
% 说明： 
% 高层绘制测点矢量，屋盖在节点图中绘制节点方向矢量
%
% 待完善：
% 绘制dxf图片？？必须要不高
%
% example：
% plotTap3F(prj,tap,0,'flagPlot',1,'dShft',[0 1.5 0]);
%
% 20230803 @lichaosz

% defaultswExe = 1;        % 默认执行该程序
defaultFlagPlot = 1;          % 默认绘制测点2D布置图片
defaultDShft = 1.5;        % 默认测点编号偏移距离

p = inputParser;
p.addRequired('bld', @isobject);  % isobject
% p.addRequired('tap', @isobject);  

validScalarPosNum = @(x) isnumeric(x);
p.addParameter('flagPlot',defaultFlagPlot,validScalarPosNum);   
p.addParameter('dShft',defaultDShft,validScalarPosNum);   

p.parse(bld,varargin{:});

dShft = p.Results.dShft;

%% 2D层测点及内法线单位矢量示意图
if p.Results.flagPlot == 1
    figure
    scaleCP  = 0.2; 
    lExt     = 3;    % 画风压系数图时向外扩展的距离
    idxTmp   = bld.tapT.iLyr==bld.nLyr;    % 取最低楼层的测点
    xLim     = [min(bld.tapT.x)-lExt max(bld.tapT.x)+lExt];
    yLim     = [min(bld.tapT.y)-lExt max(bld.tapT.y)+lExt];
    lXSubFig = max(bld.tapT.x(idxTmp)) - min(bld.tapT.x(idxTmp)) + lExt;
    lYSubFig = max(bld.tapT.y(idxTmp)) - min(bld.tapT.y(idxTmp)) + lExt;
    lYFig = 25;
    nClm = 4;
    [ha, ~] = tightSubplot(ceil(bld.nLyr/nClm), nClm, [.01 .08],[.05 .03],[.02 .02]);
    for i = 1:bld.nLyr
        axes(ha(i));
        % 外压测点
        [idxTmp1,~] = find(bld.tapT.iView<100 & bld.tapT.iLyr==i);
        plot(bld.tapT.x(idxTmp1),bld.tapT.y(idxTmp1),'bx');hold on;
        text(bld.tapT.x(idxTmp1)-dShft.*bld.tapT.xVec(idxTmp1),bld.tapT.y(idxTmp1)-dShft.*bld.tapT.yVec(idxTmp1),bld.tapT.iManu(idxTmp1),'color','k','HorizontalAlignment','center');  % ,'FontSize',fontSize1
        quiver(bld.tapT.x(idxTmp1),bld.tapT.y(idxTmp1),bld.tapT.xVec(idxTmp1),bld.tapT.yVec(idxTmp1),scaleCP,'r');hold on;
        [xWd,yWd] = tapWidth2Line(bld.tapT(idxTmp1,:));
        plot(xWd,yWd,'-+','LineWidth',2);hold on;
                
        % 内压测点
        [idxTmp2,~] = find(bld.tapT.iView>100 & bld.tapT.iLyr==i);
        plot(bld.tapT.x(idxTmp2),bld.tapT.y(idxTmp2),'b+');hold on;
        text(bld.tapT.x(idxTmp2)+dShft.*bld.tapT.xVec(idxTmp2),bld.tapT.y(idxTmp2)+dShft.*bld.tapT.yVec(idxTmp2),bld.tapT.iManu(idxTmp2),'color','b','HorizontalAlignment','center');  % ,'FontSize',fontSize1
        quiver(bld.tapT.x(idxTmp2),bld.tapT.y(idxTmp2),bld.tapT.xVec(idxTmp2),bld.tapT.yVec(idxTmp2),scaleCP,'r');hold on;

        % 平面轮廓
        for j = 1:length(bld.bound{7})
            plot(bld.bound{7}{j}.outer(:,1),bld.bound{7}{j}.outer(:,2),'-k','linewidth',1);hold on;
        end

        set(gca,'DataAspectRatio',[1 1 1]); 
        axis([xLim yLim]);
    %         xlabel({['lX正: ' num2str(lXLyrPs(i),'%.1f') ' lX负: ' num2str(lXLyrNg(i),'%.1f')],...
    %             ['lY正: ' num2str(lYLyrPs(i),'%.1f') ' lY负: ' num2str(lYLyrNg(i),'%.1f')]},'EdgeColor','none'); %'TextBox',[0.45 0.7 0.1 0.1],
        title(['层:' num2str(i) '，高：' num2str(bld.lyrT.z(i)) 'm']);
    end
    annotation('TextBox',[0.45 0.9 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ')测点位置及法向单位矢量，' bld.dgrChar{i} '°风向(蓝色为内压测点)'],'fontsize',16,'EdgeColor','none','HorizontalAlignment','center');
    set(gcf,'Units','centimeters','position',[1 2 lXSubFig/lYSubFig*lYFig*nClm/ceil(bld.nLyr/nClm) lYFig]);
    print('-vector','-dsvg',[bld.pathGeom 'figures/' bld.iBldStr '('  bld.nameCN ')测点位置及法向单位矢量2D示意图-所有层']);
    saveas(gcf,[bld.pathGeom 'figures/' bld.iBldStr '('  bld.nameCN ')测点位置及法向单位矢量2D示意图-所有层.fig']);

    lYFig = 12;
    for i = 1:bld.nLyr
        figure
        % 外压测点
        [idxTmp1,~] = find(bld.tapT.iView<100 & bld.tapT.iLyr==i);
        plot(bld.tapT.x(idxTmp1),bld.tapT.y(idxTmp1),'bx');hold on;
        text(bld.tapT.x(idxTmp1)-dShft.*bld.tapT.xVec(idxTmp1),bld.tapT.y(idxTmp1)-dShft.*bld.tapT.yVec(idxTmp1),bld.tapT.iManu(idxTmp1),'color','k','HorizontalAlignment','center');  % ,'FontSize',fontSize1
        quiver(bld.tapT.x(idxTmp1),bld.tapT.y(idxTmp1),bld.tapT.xVec(idxTmp1),bld.tapT.yVec(idxTmp1),scaleCP,'r');hold on;
        [xWd,yWd] = tapWidth2Line(bld.tapT(idxTmp1,:));
        plot(xWd,yWd,'-+','LineWidth',2);hold on;
                
        % 内压测点
        [idxTmp2,~] = find(bld.tapT.iView>100 & bld.tapT.iLyr==i);
        plot(bld.tapT.x(idxTmp2),bld.tapT.y(idxTmp2),'b+');hold on;
        text(bld.tapT.x(idxTmp2)+dShft.*bld.tapT.xVec(idxTmp2),bld.tapT.y(idxTmp2)+dShft.*bld.tapT.yVec(idxTmp2),bld.tapT.iManu(idxTmp2),'color','b','HorizontalAlignment','center');  % ,'FontSize',fontSize1
        quiver(bld.tapT.x(idxTmp2),bld.tapT.y(idxTmp2),bld.tapT.xVec(idxTmp2),bld.tapT.yVec(idxTmp2),scaleCP,'r');hold on;
        
        % 平面轮廓
        for j = 1:length(bld.bound{7})
            plot(bld.bound{7}{j}.outer(:,1),bld.bound{7}{j}.outer(:,2),'-k','linewidth',1);hold on;
        end
        
        set(gca,'DataAspectRatio',[1 1 1]); 
        axis([xLim yLim]);
        title([bld.iBldStr '('  bld.nameCN ')层:' num2str(i) '，高：' num2str(bld.lyrT.z(i)) 'm']);
        set(gcf,'Units','centimeters','position',[1 2 lXSubFig/lYSubFig*lYFig*nClm/ceil(bld.nLyr/nClm) lYFig]);
        print('-dsvg','-vector','-r300',[bld.pathGeom 'figures/' bld.iBldStr '('  bld.nameCN ')测点位置及法向单位矢量2D示意图-层' num2str(i,'%02i')]);
        saveas(gcf,[bld.pathGeom 'figures/' bld.iBldStr '('  bld.nameCN ')测点位置及法向单位矢量2D示意图-层' num2str(i,'%02i') '.fig']);
    end

else
    disp(['不重新绘制 ' bld.iBldStr '('  bld.nameCN ') 测点2D空间位置图']);
end

    function [xWd,yWd] = tapWidth2Line(t)
% 依据 测点坐标，宽度，内法线矢量，计算表示承载宽度的矢量线段坐标
        vec  = rotz(90)*[(.5*t.width.*t.xVec)';(.5*t.width.*t.yVec)';(.5*t.width.*t.zVec)';];
        xWd = [-vec(1,:);vec(1,:)] + [t.x';t.x'];
        yWd = [-vec(2,:);vec(2,:)] + [t.y';t.y'];
    end

end