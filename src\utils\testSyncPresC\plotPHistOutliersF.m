function plotPHistOutliersF(tst,pHist1,pHist,TF,time)
    

% 找出所有包含异常值的列
columnsWithOutliers = find(sum(TF, 1) > 0);
    
% 为每个包含异常值的列创建对比图
for col = columnsWithOutliers
    figure('Position', [100, 100, 1200, 400]);
    plot(time, pHist(:,col), '+r', 'MarkerSize', 8); hold on;
    plot(time, pHist1(:,col), 'ob', 'MarkerSize', 6); hold on;
    plot(time(TF(:,col)), pHist1(TF(:,col),col), 'om', 'MarkerSize', 10); hold on;
    ylim([min(pHist(:,col)) max(pHist(:,col))]);
    title(['测点 ' num2str(col) ' 异常值对比 (风向: ' tst.dgrChar{i} '°)']);
    xlabel('时间 (s)');
    ylabel('压强 (Pa)');
    legend('修正后数据', '原始数据', '异常值', 'Location', 'best');
    grid on;
    
    % 保存图片
    saveas(gcf, [tst.pathPFigPHist '测点' num2str(col) '_异常值对比_' tst.dgrChar{i} '.png']);
    close(gcf);
end

disp(['第' num2str(i) '个风向风压时程数据中 大于 ' num2str(nSigma) ' Sigma的异常采样点 数量为' num2str(nnz(TF)) '？是否必要？']);
disp(['第' num2str(i) '个风向风压时程数据 filloutliers 去除异常值后 NaN 坏点数量为' num2str(nnz(isnan(pHist)))]);

end