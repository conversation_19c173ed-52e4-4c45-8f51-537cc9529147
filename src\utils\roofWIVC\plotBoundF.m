function plotBoundF(bld, varargin)
% 绘制结构轮廓以及分区绘制示意图

% view     -- 0 不绘制轮廓
%          -- 1:6依次对应 X+ X- Y+ Y- Z+ Z-
% sizeFig  -- 高层建筑用高度(sizeFig(2))控制图片尺寸
%          -- 屋盖建筑用宽度(sizeFig(1))控制图片尺寸


% Example:
% plotBoundF(bld,1:6);
% plotBoundF(bld,'view',1:4,'sizeFig',[0 15]);

% @lichaosz 20230817

defaultIView   = 'X+';    % 默认绘制图片
defaultSizeFig  = [15 25];  % 默认 word 中A4除去2.5左右边距，则图片的宽度最大值为 21-5 = 15 cm，图片最大高度为29.7-5 = 24 cm
defaultShowTap  = 1;        % 默认显示测点位置及编号  

p = inputParser;
p.addRequired('bld', @isobject);
p.addParameter('view',  defaultIView,@isnumeric);
p.addParameter('showTap',defaultShowTap,@isnumeric);
p.addParameter('sizeFig',defaultSizeFig,@isnumeric);
p.parse(bld, varargin{:});
view    = p.Results.view;
showTap = p.Results.showTap;
sizeFig = p.Results.sizeFig;

if view == 0; disp(['不重新绘制 ' bld.iBldStr '('  bld.nameCN ') 建筑轮廓图']);return; end

widthLine = 2;
yShftText = 1;

for iView = view
    bound = bld.bound{iView};
    subFig = bound{1}.subFig;
    
    [ix, iy, cx, cy] = Dim2F(iView);
    
    fontSize = 6;
    posAx = [0.025,0.025,0.95,0.95];
    figure
    
    % 找到图像的最大最小xy坐标
    xMax = max(bound{1}.outer(:,ix));
    xMin = min(bound{1}.outer(:,ix));
    yMax = max(bound{1}.outer(:,iy));
    yMin = min(bound{1}.outer(:,iy));
    
    for i = 1:length(bound)
        xMax = max([xMax;bound{i}.outer(:,ix)]);
        xMin = min([xMin;bound{i}.outer(:,ix)]);
        yMax = max([yMax;bound{i}.outer(:,iy)]);
        yMin = min([yMin;bound{i}.outer(:,iy)]);
    end
    
    % 当有建筑整体辅助线时，需要在图片中计及其坐标
    if isfield(bound{1},'astW')
        for j = 1:length(bound{1}.astW)
            xMax = max([xMax;bound{1}.astW{j}(:,ix)]);
            xMin = min([xMin;bound{1}.astW{j}(:,ix)]);
            yMax = max([yMax;bound{1}.astW{j}(:,iy)]);
            yMin = min([yMin;bound{1}.astW{j}(:,iy)]);
        end
    end

    if bld.typeStrct == 1
        fig.height = sizeFig(2);   % 图片宽度
        fig.width  = fig.height/(yMax-yMin)*(xMax-xMin);
    elseif bld.typeStrct == 2
        fig.width  = sizeFig(1);   % 图片宽度    
        fig.height = fig.width/(xMax-xMin)*(yMax-yMin);
    end
    text(mean([xMin,xMax]),mean([yMin,yMax]),bound{1}.class,'FontAngle','italic','Color','r','FontSize',5*fontSize,'HorizontalAlignment','center');hold on;

    % 绘制轮廓，包括外轮廓，内轮廓，辅助线，建筑轮廓（辅助定位）
    for k = 1:length(bound)
        
        % 外轮廓
        xBound = bound{k}.outer(:,ix);
        yBound = bound{k}.outer(:,iy);
        if any(isnan(xBound))
            ind=find(isnan(xBound));
            plot([xBound(1:ind-1);xBound(1)],[yBound(1:ind-1);yBound(1)],'-k','linewidth',widthLine);hold on;
            plot([xBound(ind+1:end);xBound(ind+1)],[yBound(ind+1:end);yBound(ind+1)],'-k','linewidth',widthLine);hold on;
        else
            plot([xBound;xBound(1)],[yBound;yBound(1)],'-k','linewidth',widthLine);hold on;
            if strcmp(bound{k}.nameShow,'on')% && length(bound{k})>1
                text((max(xBound)+min(xBound))/2,(max(yBound)+min(yBound))/2,bound{k}.name,'FontAngle','italic','Color','b','FontSize',3*fontSize,'HorizontalAlignment','center'); 
            end
        end
        
        % 内轮廓
        if isfield(bound{k},'inner')
            for j = 1:length(bound{k}.inner)
                plot(bound{k}.inner{j}(:,ix),bound{k}.inner{j}(:,iy),'--k','linewidth',widthLine);hold on;
            end
        end

        % 辅助线
        if isfield(bound{k},'astR')
            for j = 1:length(bound{k}.astR)
                plot(bound{k}.astR{j}(:,ix),bound{k}.astR{j}(:,iy),'-k','linewidth',0.5*widthLine);hold on;
            end
        end
    end
    
    % 整体辅助线
    if isfield(bound{1},'astW')
        for j = 1:length(bound{1}.astW)
            plot([bound{1}.astW{j}(:,ix);bound{1}.astW{j}(1,ix)],[bound{1}.astW{j}(:,iy);bound{1}.astW{j}(1,iy)],'-','color',[0.5 0.5 0.5],'linewidth',0.5*widthLine);hold on;
        end
    end

    if showTap == 1
        idxTmp = bld.tapT.iView==iView;
        plot(bld.tapT.(cx)(idxTmp),bld.tapT.(cy)(idxTmp),'+r','MarkerSize',3,'MarkerFaceColor','b');hold on;
        text(bld.tapT.(cx)(idxTmp),bld.tapT.(cy)(idxTmp)-yShftText,bld.tapT.iManu(idxTmp),'FontSize',fontSize,'HorizontalAlignment','center');hold on;
    end

    axis equal;axis off;
    if (iView==2 || iView==3)   % 对 X- 和 Y+ 面的X/Y轴进行水平翻转
        set(gca,'XDir','reverse')   % 反转X轴方向
    end
    % xlabel(['X (m)']);ylabel(['Y (m)']);
    axis([xMin,xMax,0,yMax]); % yMin
    set(gca,'FontName','微软雅黑','FontSize',fontSize,'position',posAx,'TitleHorizontalAlignment','right');  %设置图形占整个画面
    %     title([num2str(i) '-' num2str(j) '-' side],'FontAngle','italic','Color','b','FontSize',2*fontSize);
    set(gcf,'Units','centimeters','position',[1 1 fig.width fig.height]);
    
    if ~isempty(varargin)
        xWidthFig  = ((xMax-xMin)/subFig(1));   % 每张图片的x坐标宽度
        yHeightFig = ((yMax-yMin)/subFig(2));   % 每张图片的y坐标高度
        if subFig(1) + subFig(2) > 2    % 大于2表示需要分区
            for i = 1:subFig(1)
                for j = 1:subFig(2)
                    fill([xMin+(i-1)*xWidthFig  xMin+i*xWidthFig xMin+i*xWidthFig xMin+(i-1)*xWidthFig],...
                         [yMin+(j-1)*yHeightFig yMin+(j-1)*yHeightFig yMin+j*yHeightFig yMin+j*yHeightFig],...
                         [0.3 0.3 0.3],'facealpha',0.2,'LineStyle','--','EdgeColor','r','linewidth',0.5);hold on; % [0.5 0.5 0.5]
                    text(xMin+(i-0.5)*xWidthFig,yMin+(j-0.5)*yHeightFig,[num2str(i) '-' num2str(j)],...
                        'FontAngle','italic','Color','r','FontSize',3*fontSize,'HorizontalAlignment','center','EdgeColor','r'); 
                end
            end
        end
        print('-vector','-dsvg',      [bld.pathGeom 'figures/结构轮廓分区绘制示意图' num2str(subFig(1)*10+subFig(2),'%2i') bound{1}.class]);
        % print('-dpng','-image','-r300',[bld.pathGeom 'figures/结构轮廓分区绘制示意图' num2str(subFig(1)*10+subFig(2),'%2i') bound{1}.class]);
    else
        print('-vector','-dsvg',      [bld.pathGeom 'figures/结构轮廓分区绘制示意图11' bound{1}.class]);
        % print('-dpng','-image','-r300',[bld.pathGeom 'figures/结构轮廓分区绘制示意图11' bound{1}.class]);
    end
end

end