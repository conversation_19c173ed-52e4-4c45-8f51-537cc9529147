function [ ] = txt2cPF(tst, varargin)

%   功能：
%   读取风压记录txt文件，并处理成测点压力系数时程文件，格式mat，提高后续多次处理调整数据的效率
%
%   此段程序与测点编号无关，仅使用静压、总压将采集压力转换为风压系数。这样做的目的是当一次试验中所有测点数超过512，
%   需要通过多次试验完成时，将多次试验数据合并后，数据文件中的序号不乱。
%
%   输入参数:
%     tst - 测试对象
%     Name-Value 参数:
%       'flagRun' - 是否处理测点风压txt至风压系数mat，0: 不处理 (默认),1: 处理
%    swPTapHistPlot    % 是否画测点压力时程图 0=不画， 1=画
%
%   示例:
%     txt2cPF(tst);                    % 使用默认参数，不处理
%     txt2cPF(tst, 'flagRun', 0);      % 不处理
%     txt2cPF(tst, 'flagRun', 1);      % 处理
%
%   20230820  @lichaosz  

% 解析输入参数
p = inputParser;
p.addParameter('flagPlotPTapHist', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.addParameter('flagRun', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.parse(varargin{:});
flagRun          = p.Results.flagRun;
flagPlotPTapHist = p.Results.flagPlotPTapHist;

if flagRun == 0
    disp('不重新处理 测点风压txt至风压系数mat');
    return;
end

tic
nChnlSel = length(tst.iBlockSlct)*tst.nChnlPer;

for i = tst.iDgrProc    % parfor

    file         = [tst.pathPTxt tst.dgrChar{i} '.' tst.dataFileTail];
    [pHist,time] = readWTdatF(tst,tst.nChnl,file);

    % ------------- 保留正常工作阀块的数据，删除不正常阀块的数据 ----------
    if ~isempty(tst.iBlockSlct)
        pHistTmp = [];
        for j = tst.iBlockSlct
            pHistTmp = [pHistTmp pHist(:,(tst.nChnlPer*(j-1)+1:tst.nChnlPer*j))];
        end
        pHist1 = pHistTmp;
    end

    % ------------- 去除异常值数据，5倍sigma以上，用上一个时间点值替换 ----------
    nSigma = 10;
    [pHist,TF] = filloutliers(pHist1,'center','median','ThresholdFactor',nSigma); 
    [columnOutliersMax,iiTmp] = max(sum(TF, 1));
    
    figure
    % iiTmp = 87;
    plot(time,pHist(:,iiTmp),'+k','MarkerSize',8);hold on;
    % plot(time,pHist1(:,iiTmp),'ob','MarkerSize',6);hold on;
    plot(time(TF(:,iiTmp)),pHist(TF(:,iiTmp),iiTmp),'or','MarkerSize',10);hold on;
    ylim([min(pHist1(:,iiTmp)) max(pHist1(:,iiTmp))]);

    legend({'修正后数据','修正前数据','修正数据点'})

    disp(['第' num2str(i) '个风向风压时程数据中 大于 ' num2str(nSigma) ' Sigma的异常采样点 数量为' num2str(nnz(TF)) '？是否必要？']);
    disp(['第' num2str(i) '个风向风压时程数据 filloutliers 去除异常值后 NaN 坏点数量为' num2str(nnz(isnan(pHist)))]);

    % ------------- 处理为风压系数 ----------
    cP       = (pHist-tst.pStcMeanAll)./tst.pDynMeanAll;   % 风压系数 时程
    cPMean   = mean(cP);                                   % 风压系数 均值
    cPStd    = std(cP);                                    % 风压系数 标准差
    
    parSaveF([tst.pathPMat 'cP' tst.dgrChar{i}], time, 'time', cP, 'cP');
    
    % ---------- 测压结果画图判别 ----------
    hF1 = figure(3*i-2);
    if flagPlotPTapHist == 1
        for j = 1:length(cPMean)
            plot(time,pHist(:,j),'-','linewidth',1);hold on;grid on;
        end
        set(gca,'position',[0.04 0.12 0.80 0.8]);
        legend(['静压-均值:' num2str(tst.pStcMeanAll,'%.3f')],['总压-均值:' num2str(tst.pTotalMeanAll,'%.3f')],...
            'fontsize',12,'position',[0.87 0.75 0.1 0.1]);
        annotation('TextBox',[0.86 0.35 0.1 0.1],'string',{['风向: ' tst.dgrChar{i} '°'],['动压: ' num2str(tst.pDynMeanAll,'%.3f')]},'fontsize',12,'EdgeColor','none');
        xlabel('时间 (s)');ylabel('压强 (Pa)');
        set(gcf,'Units','centimeters','position',[1 1 4*10 10]);
    	% saveas(gcf,[tst.pathPFigPHist '测点风压时程' tst.dgrChar{i} '.jpg']);
        print('-dpng','-r300',[tst.pathPFigPHist '测点风压时程' tst.dgrChar{i}]);
    end

    hF2 = figure(3*i-1);
    plot(1:nChnlSel,cPMean,'-sk');hold on; grid on;
%     plot(iTapErr,cPMean(iTapErr),'sr');hold on;
    set(gca,'position',[0.07 0.12 0.88 0.8],'xtick',[0:32:512]);
    annotation('TextBox',[0.48 0.90 0.1 0.1],'String',{['风向:' tst.dgrChar{i} '°']},'fontsize',12,'EdgeColor','none');
    ylim([-2 1.2]);xlim([0 512]);
    xlabel('测点通道号');ylabel('风压系数');
    set(gcf,'Units','centimeters','position',[1 2 40 10]);
    saveas(gcf,[tst.pathPFigPMean '测点平均风压系数' tst.dgrChar{i} '.svg']);
    saveas(gcf,[tst.pathPFigPMean '测点平均风压系数' tst.dgrChar{i} '.fig']);
    
    hF3 = figure(3*i);
    plot(1:nChnlSel,cPStd,'-sk');hold on; grid on;   
%     plot(iTapErr,cPStd(iTapErr),'sr');hold on; grid on;
    set(gca,'position',[0.07 0.12 0.88 0.8],'xtick',[0:32:512]);
    annotation('TextBox',[0.48 0.90 0.1 0.1],'String',{['风向:' tst.dgrChar{i} '°']},'fontsize',12,'EdgeColor','none');
    xlim([0 512]);
    xlabel('测点通道号');ylabel('脉动风压系数');
    set(gcf,'Units','centimeters','position',[1 13 40 10]);
    saveas(gcf,[tst.pathPFigPStd '测点脉动风压系数' tst.dgrChar{i} '.svg']);
    saveas(gcf,[tst.pathPFigPStd '测点脉动风压系数' tst.dgrChar{i} '.fig']);
    
    % disp(['1. ' tst.dgrChar{i} ' pHist txt is converted to cP Mat; 2. figures of cPMean and cPStd saved.']);
    close(hF1,hF2,hF3);
end

disp(['iTst=' num2str(tst.iTst) '的cPHist处理完成']);
disp(['1. pHist txt is converted to cP Mat; 2. figures of cPMean and cPStd saved.']);

toc
% for i = [1:9:36]
%     eval(['winopen tst.pathPFigPMean' '测点平均风压系数' tst.dgrChar{i} '.fig'])
% end
% winopen [tst.pathPFigPStd '测点脉动风压系数000.fig']
% winopen .\figures\
end
