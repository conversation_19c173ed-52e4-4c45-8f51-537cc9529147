function [pStcMeanAll,pDynMeanAll,pTotalMeanAll] = pRefPrcsF(tst, varargin)
% 处理参考总压和静压通道的数据，计算来流风速，动压
%
% 风洞试验中 总压 静压 等参考压数据处理，为后续处理准备
% 在刘红军老师风洞中，总压静压将皮托管摆放建筑前方，在试验前和试验后分别测量一次，试验中移走总压静压皮托管，原因是风洞中部风速小，越靠近洞壁风洞越大
%
% 输入参数:
%   tst - 测试对象
%   Name-Value 参数:
%     'flagRun' - 是否重新处理总压静压数据
%                 0: 不重新处理，若存在处理结果则直接加载 (默认)
%                 1: 强制重新处理，无论是否存在处理结果都重新计算
%
% 示例:
%   tst = pRefPrcsF(tst);                    % 使用默认参数
%   tst = pRefPrcsF(tst, 'flagRun', 0);      % 若存在处理结果则直接加载，否则处理
%   tst = pRefPrcsF(tst, 'flagRun', 1);      % 强制重新处理总压静压数据
%
% 返回值:
%   tst - 更新后的测试对象，包含处理后的总压静压数据

% 解析输入参数
p = inputParser;
p.addParameter('flagRun', 0, @(x) isnumeric(x) && (x==0 || x==1));
p.parse(varargin{:});

flagRun = p.Results.flagRun;

if exist([tst.pathPMat 'pRefMean.mat'],'file')==2 && flagRun==0    % 若存在 pRefMean.mat 文件，则不再每次处理总静压文件
    load([tst.pathPMat 'pRefMean.mat'],'pDynMeanAll','pStcMeanAll','pTotalMeanAll');
    disp('不重新处理 总/静压数据');
elseif exist([tst.pathPMat 'pRefMean.mat'],'file')==0 || flagRun==1   % 若不存在 pRefMean.mat 文件，则正常处理总静压文件
    % if exist([tst.filePStcTot num2str(tst.snPTotStc(1)) '.' tst.dataFileTail],'file')==0      % 判断总压静压原始记录txt文件是否存在
    %     warning([tst.filePStcTot num2str(tst.snPTotStc(1)) '.' tst.dataFileTail ' file for total/staic pressure dose not exist.']);
    %     return
    % end

    nTest    = length(tst.snPTotStc);
    colorSet = varycolor(2*nTest);
    nFig     = 2000+randi(100);
    [pTotalMean,pStcMean,pDynMean,uDynRef] = deal(zeros(nTest,1));
    % ---------------------- 处理 总压静压 提前单独测量的工况 ---------------------------
    for i = 1:nTest
        file         = [tst.filePStcTot num2str(tst.snPTotStc(i)) '.' tst.dataFileTail];
        [pHist,time] = readWTdatF(tst,tst.nChnlPStcTot,file);

        % ---------- 依据测压管信息将数据分配 ----------
        timeSel= time(end-tst.nSel+1:end);
        pTotal = pHist(end-tst.nSel+1:end,tst.nPTotal);      % 总压
        pStc   = pHist(end-tst.nSel+1:end,tst.nPStc);        % 静压
        pTotalMean(i) = mean(pTotal);                         % 各次测量的平均总压
        
        if isempty(tst.pStcMeanPre)
            pStcMean(i)   = mean(pStc);                       % 各次测量的平均静压
        else
            pStcMean(i)   = tst.pStcMeanPre;                  % 采用预设的平均静压值
        end     
        
        pDynMean(i)   = pTotalMean(i) - pStcMean(i);          % 各次测量的平均动压
        uDynRef(i)    = (pDynMean(i)*2/tst.rhoAir)^0.5;       % 动压，参考高度出的平均风速

        figure(nFig);
        hP(2*i-1) = plot(timeSel,pStc,'-','color',colorSet(2*i-1,:),'linewidth',1);hold on;grid on;
        hP(2*i)   = plot(timeSel,pTotal,'-','color',colorSet(2*i,:),'linewidth',1);hold on;grid on;
        lgdStr{2*i-1} = ['静压-' num2str(i) ' : ' num2str(pStcMean(i),'%.2f')];
        lgdStr{2*i}   = ['总压-' num2str(i) ' : ' num2str(pTotalMean(i),'%.2f')];
    end

    if isempty(tst.pStcMeanPre)
    	pStcMeanAll   = mean(pStcMean);
    else
        pStcMeanAll   = tst.pStcMeanPre;
    end
            
    pTotalMeanAll = mean(pTotalMean);
    pDynMeanAll   = mean(pDynMean)*tst.ratiopDynTune;        % !!!! 动压调整，其实不太应该，京基项目中的问题是负压太大了 20220120
    uDynRefAll    = (pDynMeanAll*2/tst.rhoAir)^0.5;

    annotation('TextBox',[0.86 0.25 0.1 0.1],'string',{['静压-均值: ' num2str(pStcMeanAll,'%.2f')],['总压-均值: ' num2str(pTotalMeanAll,'%.2f')]...
        ['动压-均值: ' num2str(pDynMeanAll,'%.2f')],['参考风速:  ' num2str(uDynRefAll,'%.2f')],['ratiopDynTune=  ' num2str(tst.ratiopDynTune,'%.2f')]},'fontsize',12,'EdgeColor','none');

    setMyFig(gcf,'时间 (s)','压强 (Pa)',['iTst = ' num2str(tst.iTst)],[0.04 0.12 0.80 0.8],12,...
         hP,lgdStr,[0.85 0.70 0.1 0.1],[1 1 4*10 12],[tst.pathPFig '总压静压时程.fig'],[],[],[],[]);

    save([tst.pathPMat 'pRefMean'],'pStcMean','pTotalMean','pDynMean','uDynRef','pStcMeanAll','pTotalMeanAll','pDynMeanAll','uDynRefAll');
    
    if ~isempty(tst.pStcMeanPre)
        figure
        for i = 1:length(tst.snPStill)
            file      = [tst.filePStill num2str(tst.snPStill(i)) '.' tst.dataFileTail];
            [pHist,~] = readWTdatF(tst,tst.nChnl,file);
            pMean = mean(pHist);
            hP2(i) = plot(pMean,'-s','color',colorSet(i,:),'linewidth',1);hold on;grid on;
            lgdStr2{i}   = ['无风' num2str(i)];
        end
        setMyFig(gcf,'通道号','压强 (Pa)',['无风风压均值（' 'iTst = ' num2str(tst.iTst) '）'],[0.04 0.12 0.92 0.8],12,...
            hP2,lgdStr2,[0.86 0.80 0.1 0.1],[1 1 4*10 12],[tst.pathPFig '无风' num2str(tst.snPStill(i)) '风压均值所有测点.fig'],[0 512],[],[],[]);
    end
end

end