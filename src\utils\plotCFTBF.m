function [ ] = plotCFTBF(bld,swExe)
% plot Coefficients of wind Forces of Tall Building 
%
% 功能
% 1、先汇总各风向下的楼层荷载，保存平均风荷载供后续响应计算使用
% 2、将各风向下的楼层荷载系数绘制成图片，便于观察

if swExe == 0; disp(['不重新绘制 ' bld.iBldStr '('  bld.nameCN ')  楼层风荷载图']);return; end   % 是否执行该程序

marker = 'osd*x+^v><ph';

nDgr = bld.nDgr;
dDgr = bld.dDgr;

nClmSubFig = bld.nClmSubFig;
nRowSubFig = bld.nRowSubFig;

[muSAll] = deal(zeros(bld.nLyr,nDgr,bld.nDofFlr));
[fLyrMeanAll,cFLyrMeanAll,cFLyrStdAll] = deal(zeros(bld.nLyr,bld.nDofFlr,nDgr));
[fFlrMeanAll,cFFlrMeanAll,cFFlrStdAll] = deal(zeros(bld.nFlr,bld.nDofFlr,nDgr));

for i = 1:nDgr
    load([bld.pathLoad 'fStat',bld.dgrChar{i},'.mat'],'muSLyr','fFlrMean','cFFlrMean','cFFlrStd','fLyrMean','cFLyrMean','cFLyrStd');
    muSAll(:,i,:) = muSLyr;
    fLyrMeanAll(:,:,i)  = fLyrMean;
    cFLyrMeanAll(:,:,i) = cFLyrMean;
    cFLyrStdAll(:,:,i)  = cFLyrStd;
    fFlrMeanAll(:,:,i)  = fFlrMean;
    cFFlrMeanAll(:,:,i) = cFFlrMean;
    cFFlrStdAll(:,:,i)  = cFFlrStd;
end
save([bld.pathLoad 'fAllFlrMean.mat'],'fFlrMeanAll');

%% 各风向下的体型系数 
figure
muSBld = squeeze(mean(muSAll,1));
for i = 1:nDgr
    theta = (i-1)*dDgr;
    muSBldAlong(i)  = muSBld(i,1)*sind(theta) - muSBld(i,2)*cosd(theta);
end
hP(1) = plot(dDgr*(0:nDgr-1),muSBldAlong,'ko','MarkerFaceColor','k');hold on;grid on;
hP(2) = plot(dDgr*(0:nDgr-1),muSBld(:,1),'rs','MarkerFaceColor','r');hold on;grid on;
hP(3) = plot(dDgr*(0:nDgr-1),muSBld(:,2),'bd','MarkerFaceColor','b');hold on;grid on;
set(gca,'xtick',[0:30:360],'ytick',[-2:0.2:2],'fontsize',10);
ylim([-2 2]);xlim([0 360]);
xlabel('风向角（°）');ylabel('体型系数');
multiLgdF(hP,{'顺风向','X向','Y向'},[.15,.45,.75;.92,.92,.92],10);
annotation('TextBox',[0.4 0.82 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ')'],'fontsize',12,'EdgeColor','none');
set(gcf,'Units','centimeters','position',[1 2 16 9]);
print('-dsvg','-vector',[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 整体体型系数VS风向.svg']);

%% 基底剪力系数均值 VS 风向
figure
cShrBaseMean = reshape(mean(cFFlrMeanAll),[bld.nDofFlr,nDgr]);
hP(1) = plot(dDgr*(0:nDgr-1),cShrBaseMean(1,:),'-r','marker',marker(1));hold on; grid on;
hP(2) = plot(dDgr*(0:nDgr-1),cShrBaseMean(2,:),'-b','marker',marker(2));hold on; grid on;
hP(3) = plot(dDgr*(0:nDgr-1),cShrBaseMean(3,:),'-k','marker',marker(3));hold on; grid on;
set(gca,'xtick',[0:30:360],'fontsize',10);
ylim([-2 2]);xlim([0 360]);
xlabel('风向角（°）');ylabel('基底剪力系数均值');
multiLgdF(hP,{'X','Y','RZ'},[.15,.45,.75;.92,.92,.92],10);
annotation('TextBox',[0.4 0.82 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ')'],'fontsize',12,'EdgeColor','none');
set(gcf,'Units','centimeters','position',[1 2 12 9]);
print('-dsvg','-vector',[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 基底剪力系数均值VS风向.svg']);

%% 基底剪力脉动系数标准差 VS 风向
figure
cShrBaseStd = reshape(mean(cFFlrStdAll),[bld.nDofFlr,nDgr]);
hP(1) = plot(dDgr*(0:nDgr-1),cShrBaseStd(1,:),'-r','marker',marker(1));hold on; grid on;
hP(2) = plot(dDgr*(0:nDgr-1),cShrBaseStd(2,:),'-b','marker',marker(2));hold on; grid on;
hP(3) = plot(dDgr*(0:nDgr-1),cShrBaseStd(3,:),'-k','marker',marker(3));hold on; grid on;
set(gca,'xtick',[0:30:360],'fontsize',10);
ylim([0 0.5]);xlim([0 360]);
xlabel('风向角（°）');ylabel('基底剪力系数标准差');
multiLgdF(hP,{'X','Y','RZ'},[.15,.45,.75;.92,.92,.92],10);
annotation('TextBox',[0.4 0.82 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ')'],'fontsize',12,'EdgeColor','none');
set(gcf,'Units','centimeters','position',[13 2 12 9]);
print('-dsvg','-vector',[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 基底剪力脉动系数标准差VS风向.svg']);

%% 楼层风荷载平均系数
figure
[ha, pos] = tightSubplot(nRowSubFig, nClmSubFig, [.05 .01],[.05 .06],[.03 .01]);
for i = 1:bld.nTwr
    iFlrTwr{i} = bld.flrT.iTwr == i;
    iLyrTwr{i} = bld.lyrT.iTwr == i;
end

for i = 1:nRowSubFig
    for j = 1:nClmSubFig
        k = (i-1)*nClmSubFig+j;
        axes(ha(k));
        for m = 1:bld.nTwr
            hP(6*m-5) = plot(cFLyrMeanAll(iLyrTwr{m},1,k),bld.lyrT.z(iLyrTwr{m}),'marker',marker(3*m-2),'color','r','MarkerFaceColor','r','MarkerEdgeColor','r','MarkerSize',8);hold on; 
            hP(6*m-4) = plot(cFLyrMeanAll(iLyrTwr{m},2,k),bld.lyrT.z(iLyrTwr{m}),'marker',marker(3*m-1),'color','k','MarkerFaceColor','k','MarkerEdgeColor','k','MarkerSize',8);hold on;
            hP(6*m-3) = plot(cFLyrMeanAll(iLyrTwr{m},3,k),bld.lyrT.z(iLyrTwr{m}),'marker',marker(3*m  ),'color','b','MarkerFaceColor','b','MarkerEdgeColor','b','MarkerSize',8);hold on;
            hP(6*m-2) = plot(cFFlrMeanAll(iFlrTwr{m},1,k),bld.flrT.z(iFlrTwr{m}),'marker',marker(3*m-2),'color','r');hold on;
            hP(6*m-1) = plot(cFFlrMeanAll(iFlrTwr{m},2,k),bld.flrT.z(iFlrTwr{m}),'marker',marker(3*m-1),'color','k');hold on;
            hP(6*m  ) = plot(cFFlrMeanAll(iFlrTwr{m},3,k),bld.flrT.z(iFlrTwr{m}),'marker',marker(3*m  ),'color','b');hold on;

            lgdStr{6*m-5} = ['X-测点层-塔' num2str(m)];
            lgdStr{6*m-4} = ['Y-测点层-塔' num2str(m)];
            lgdStr{6*m-3} = ['Z-测点层-塔' num2str(m)];
            lgdStr{6*m-2} = ['X-楼层-塔' num2str(m)];
            lgdStr{6*m-1} = ['Y-楼层-塔' num2str(m)];
            lgdStr{6*m  } = ['Z-楼层-塔' num2str(m)];
        end
        plot([-1.4,-1.4],[-100,1e3],'--m','linewidth',1.5);
        plot([1.4,1.4],[-100,1e3],'--m','linewidth',1.5);
        xlabel([num2str((k-1)*dDgr,'%3i') '°'],'fontsize',10);
        if j == 1; ylabel('楼层高度(m)','fontsize',10); end
%         if j ~= 1; set(gca,'ytick',[]); end
        set(gca,'box','off','xlim',[-2 2],'ylim',[0 bld.hArch+10],'xtick',[-2,-1.4,-1.0,0,1.0,1.4,2.0]);
        grid on;
    end
end
multiLgdF(hP,lgdStr,[.04 .11 .18 .04 .11 .18 .25 .32 .39 .25 .32 .39; .93 .93 .93 .91 .91 .91 .93 .93 .93 .91 .91 .91],10);
annotation('TextBox',[0.7 0.9 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ') 各风向下的楼层风荷载平均系数C_{f,mean}'],'fontsize',20,'EdgeColor','none');
set(gcf,'Units','centimeters','position',[0 2 50 25]);
% print('-dpng','-opengl','-r300',['./figures/楼层风荷载平均系数.png']);
saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 楼层风荷载平均系数.fig']);

%% 楼层风荷载均方根系数
figure
[ha, pos] = tightSubplot(nRowSubFig, nClmSubFig, [.05 .01],[.05 .06],[.03 .01]);
for i = 1:nRowSubFig
    for j = 1:nClmSubFig
        k = (i-1)*nClmSubFig+j;
        axes(ha(k));
        hP(1) = plot(cFLyrStdAll(:,1,k),bld.lyrT.z,'o', 'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'r');hold on;
        hP(2) = plot(cFLyrStdAll(:,2,k),bld.lyrT.z,'s', 'MarkerFaceColor', 'k', 'MarkerEdgeColor', 'k');hold on;
        hP(3) = plot(cFLyrStdAll(:,3,k),bld.lyrT.z,'d', 'MarkerFaceColor', 'b', 'MarkerEdgeColor', 'b');hold on;
        hP(4) = plot(cFFlrStdAll(:,1,k),bld.flrT.z,'or');hold on;
        hP(5) = plot(cFFlrStdAll(:,2,k),bld.flrT.z,'sk');hold on;
        hP(6) = plot(cFFlrStdAll(:,3,k),bld.flrT.z,'db');hold on;
        xlabel([num2str((k-1)*dDgr,'%3i') '°'],'fontsize',10);
        if j == 1; ylabel('楼层高度(m)','fontsize',10); end
%         if j ~= 1; set(gca,'ytick',[],'ygrid','on'); end
        set(gca,'box','off','xlim',[0 0.4],'ylim',[0 bld.hArch+10],'xtick',[0:0.1:0.4]);
        grid on;
    end
end
multiLgdF(hP,{'X-测点层','Y-测点层','RZ-测点层','X-楼层','Y-楼层','RZ-楼层'},[.1 .16 .22 .1 .16 .22; .93 .93 .93 .91 .91 .91],10);
annotation('TextBox',[0.4 0.9 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ') 各风向下的楼层风荷载均方根系数C_{f,rms}'],'fontsize',20,'EdgeColor','none');
set(gcf,'Units','centimeters','position',[0 2 50 25]);
% print('-dpng','-opengl','-r300',['./figures/楼层风荷载均方根系数.png']);
saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 楼层风荷载均方根系数.fig']);

%% 层最大-最小体型系数
figure
hDlt = 20;
yyaxis left
hP(1) = plot(max(muSAll(:,:,1),[],2),bld.lyrT.z,'sr');hold on;
hP(2) = plot(min(muSAll(:,:,1),[],2),bld.lyrT.z,'or');hold on;
hP(3) = plot(max(muSAll(:,:,2),[],2),bld.lyrT.z,'sk');hold on;
hP(4) = plot(min(muSAll(:,:,2),[],2),bld.lyrT.z,'ok');hold on;grid on;
xlabel('全风向层体型系数极值');ylabel('测点层数');
% set(gca,'ylim',[0 ceil(bld.hArch/hDlt)*hDlt],'ytick',flipud(bld.lyrT.z),'YTickLabels',num2cell(flipud(bld.lyrT.i)));
yyaxis right
plot([1.4 1.4],[0 ceil(bld.hArch/hDlt)*hDlt],'--m','LineWidth',1.5);hold on;
plot([-1.4 -1.4],[0 ceil(bld.hArch/hDlt)*hDlt],'--m','LineWidth',1.5);hold on;
set(gca,'ylim',[0 ceil(bld.hArch/hDlt)*hDlt],'Position',[.11 .11 .73 .78]);grid on;  % ,'ytick',flipud(bld.lyrT.z)
ylabel('测点层高');
multiLgdF(hP(1:4),{'X Max','X Min','Y Max','Y Min'},[.25,.65,.25,.65;.92,.92,.87,.87],10);
annotation('TextBox',[0.35 0.08 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ')'],'fontsize',12,'EdgeColor','none');
xlim([-2.5 2.5]);
set(gcf,'Units','centimeters','position',[36 2 10 14]);
print('-dsvg','-r300',[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 层最大-最小体型系数.svg']);
saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ') 层最大-最小体型系数.fig']);


end
