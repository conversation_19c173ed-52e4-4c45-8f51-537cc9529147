function [] = plotcPLyrF(bld,tst,tap,varargin)
%
% 功能：
% 按测点层绘制测点的风压系数统计量曲线，目前先绘制外压测点吧，方便对比
% 用途：
% 针对高层建筑结构，将测点按高度层分别绘出，检查试验整体质量，方便查找有问题的测点
% 例子：
% plotcPLyrF(bld,tst,1);
%
% 20230820 @lichaosz

close all; % 避免之前打开的figure与本程序中的冲突，绘制在一起。
disp('plotcPLyrF需要关闭当前所有图片窗口!');

defaultFlgPlot = 1;          % 默认绘制测点2D布置图片
defaultDShft = 3;        % 默认测点编号偏移距离

p = inputParser;
p.addRequired('bld', @isobject);  % isobject
p.addRequired('tst', @isobject);  % isobject
p.addRequired('tap', @isobject);  % isobject
validScalarPosNum = @(x) isnumeric(x);
p.addParameter('flgPlot',defaultFlgPlot,validScalarPosNum);   
p.addParameter('dShft',defaultDShft,validScalarPosNum);   

p.parse(bld,tst,tap,varargin{:});

dShft = p.Results.dShft;

if p.Results.flgPlot == 0; disp(['不重新绘制 ' bld.iBldStr '('  bld.nameCN ') 测点层风压矢量及平均脉动风压系数图']);return; end

scaleCP  = 1; 
lExt     = 10;    % 画风压系数图时向外扩展的距离
idxTmp   = bld.tapT.iLyr == bld.nLyr;     % 取最低楼层的测点
xLim     = [min(bld.tapT.x)-lExt max(bld.tapT.x)+lExt];
yLim     = [min(bld.tapT.y)-lExt max(bld.tapT.y)+lExt];
lXSubFig = max(bld.tapT.x(idxTmp)) - min(bld.tapT.x(idxTmp)) + lExt;
lYSubFig = max(bld.tapT.y(idxTmp)) - min(bld.tapT.y(idxTmp)) + lExt;
xyCtrGeom = [mean(bld.tapT.x(idxTmp)) mean(bld.tapT.y(idxTmp))];
lYFig = 25;
fontSize1 = 7;

% xyTrans = bld.xyOTapInFEA - bld.xyCtrFlr(1,:);     %  需要将测点坐标系的原点移到楼层几何中心位置，才可以再用测点角坐标系下的角度值排序绘制轮廓
[thetaTmp,rhoTmp]=cart2pol(bld.tapT.x - xyCtrGeom(1),bld.tapT.y - xyCtrGeom(2));

[iSameLyrCell,iSameLyrSort] = deal(cell(bld.nLyr,1));
for i = 1:bld.nLyr
    iSameLyrCell{i} = find(bld.tapT.iLyr==i);
    [~,ind] = sortrows([thetaTmp(iSameLyrCell{i}) rhoTmp(iSameLyrCell{i})],[1 2],{'descend' 'ascend'});
    iSameLyrSort{i} = iSameLyrCell{i}(ind);
end
    
parfor i = tst.iDgrProc   % parfor
    cPS = load([tap.path 'cP' bld.dgrChar{i}],'cP');   
    cP  = cPS.cP(:,bld.tapT.iTap);
    cPMean = mean(cP);                                 % 风压系数 均值
    cPStd  = std(cP);                                  % 风压系数 标准差

    %% 测点层风压系数矢量图
    hF1 = figure(3*i-2);
    [xVecArrow,yVecArrow] = rotVecF([0;-1],(i-1)*bld.dDgr+bld.gamma2);
    nClm = 4;

    [ha, pos] = tightSubplot(ceil(bld.nLyr/nClm), nClm, [.02 .015],[.02 .06],[.02 .02]);
    for j = 1:bld.nLyr
        axes(ha(j));  %,'xcolor',[0.8 0.8 0.8]
        iSameLyr = [iSameLyrSort{j};iSameLyrSort{j}(1)];
        plot(bld.tapT.x(iSameLyr),bld.tapT.y(iSameLyr),'s','color',[0.5 0.5 0.5],'linewidth',1);hold on; grid on; %,'markerfacecolor','k'
        [idxTmp1,~] = find(bld.tapT.iView<100 & bld.tapT.iLyr==j);
        quiver(bld.tapT.x(idxTmp1),bld.tapT.y(idxTmp1),cPMean(idxTmp1)'.*bld.tapT.xVec(idxTmp1),cPMean(idxTmp1)'.*bld.tapT.yVec(idxTmp1),scaleCP,'-r','AutoScale','off','LineWidth',2);hold on; grid on;
        text(bld.tapT.x(idxTmp1)-dShft.*bld.tapT.xVec(idxTmp1),bld.tapT.y(idxTmp1)-dShft.*bld.tapT.yVec(idxTmp1),bld.tapT.iManu(idxTmp1),'color','k','HorizontalAlignment','center','FontSize',fontSize1);  % 
        % 内压测点
        [idxTmp2,~] = find(bld.tapT.iView>100 & bld.tapT.iLyr==j);
        if ~isempty(idxTmp2)
            quiver(bld.tapT.x(idxTmp2),bld.tapT.y(idxTmp2),cPMean(idxTmp2)'.*bld.tapT.xVec(idxTmp2),cPMean(idxTmp2)'.*bld.tapT.yVec(idxTmp2),scaleCP,'-b','AutoScale','off');hold on; grid on;
        end
        text(bld.tapT.x(idxTmp2)+dShft.*bld.tapT.xVec(idxTmp2),bld.tapT.y(idxTmp2)+dShft.*bld.tapT.yVec(idxTmp2),bld.tapT.iManu(idxTmp2),'color','b','HorizontalAlignment','center','FontSize',fontSize1);  % 

        % 平面轮廓
        plot(bld.bound{7}{1}.outer(:,1),bld.bound{7}{1}.outer(:,2),'-','color',[0 0 0],'linewidth',0.5);hold on;

        set(gca,'DataAspectRatio',[1 1 1]); 
        axis([xLim yLim]);
        set(gca,'xtick',[],'ytick',[]);
        title(['层:' num2str(j) '，高：' num2str(bld.lyrT.z(j)) 'm']);
        % 蓝色 风向 箭头
        annotation('arrow','position',[pos{j}(1)+pos{j}(3)*.05 pos{j}(2)+pos{j}(4)*.95 xVecArrow*0.03 yVecArrow*0.03],'color','b','HeadStyle','plain');  
        % 测点坐标系示意箭头
        quiver(0,0,5,0,'-k','AutoScale','on');hold on; grid on;
        quiver(0,0,0,5,'-k','AutoScale','on');hold on; grid on;
        text(5, 0,'X');
        text(0, 5,'Y');
    end
    annotation('TextBox',[0.45 0.9 0.1 0.1],'string',[bld.iBldStr '('  bld.nameCN ')测点层风压系数矢量图(蓝色为内压测点)，' bld.dgrChar{i} '°风向'],'fontsize',16,'EdgeColor','none','HorizontalAlignment','center');
    set(gcf,'Units','centimeters','position',[1 2 lXSubFig/lYSubFig*lYFig*nClm/ceil(bld.nLyr/nClm) lYFig]);
    saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ')测点层风压系数矢量图' bld.dgrChar{i} '.svg']);
    saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ')测点层风压系数矢量图' bld.dgrChar{i} '.fig']);
    
    %% 测点层平均风压系数
	hF2 = figure(3*i-1);
    nClm = 4;
    [ha, pos] = tightSubplot(ceil(bld.nLyr/nClm), nClm, [.035 .015],[.03 .03],[.02 .01]);
    for j = 1:bld.nLyr
        axes(ha(j));
        nVec = 1:length(iSameLyrSort{j});
        plot(nVec,cPMean(iSameLyrSort{j}),'-sk');hold on;
        idxTmp = find(cPMean(iSameLyrSort{j})>0.9);
        plot(nVec(idxTmp),cPMean(iSameLyrSort{j}(idxTmp)),'or','MarkerSize',8,'LineWidth',2);hold on; grid on;
% %     plot(iTapErr,cPMean(iTapErr),'sr');hold on;   % 将来将修正的测点信息放进来
        text(length(iSameLyrSort{j})/2.2,1,['塔楼' num2str(bld.lyrT.iTwr(j)) '，层' num2str(j) '，高' num2str(bld.lyrT.z(j)) '米']);
        ylim([-1.5 1.5]);xlim([1 length(iSameLyrSort{j})]);
        set(gca,'xtick',1:length(iSameLyrSort{j}),'xticklabel',bld.tapT.iManu(iSameLyrSort{j}));
    end
    annotation('TextBox',[0.45 0.90 0.1 0.1],'String',{[bld.iBldStr '('  bld.nameCN ')测点层平均风压系数，风向:' bld.dgrChar{i} '°']},'fontsize',12,'EdgeColor','none','HorizontalAlignment','center');
    set(gcf,'Units','centimeters','position',[0 2 60 30]);
    saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ')测点层平均风压系数' bld.dgrChar{i} '.svg']);
    saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ')测点层平均风压系数' bld.dgrChar{i} '.fig']);
    
    %% 测点层脉动风压系数
	hF3 = figure(3*i);
    nClm = 4;
    [ha, pos] = tightSubplot(ceil(bld.nLyr/nClm), nClm, [.035 .015],[.03 .03],[.02 .01]);
    for j = 1:bld.nLyr
        axes(ha(j));
        plot(1:length(iSameLyrSort{j}),cPStd(iSameLyrSort{j}),'-sk');hold on; grid on;
% %     plot(iTapErr,cPMean(iTapErr),'sr');hold on;
        text(length(iSameLyrSort{j})/2.2,0.45,['塔楼' num2str(bld.lyrT.iTwr(j)) '，层' num2str(j) '，高' num2str(bld.lyrT.z(j)) '米']);
        ylim([0 0.5]);xlim([1 length(iSameLyrSort{j})]);
        set(gca,'xtick',1:length(iSameLyrSort{j}),'xticklabel',bld.tapT.iManu(iSameLyrSort{j}));
    end
    annotation('TextBox',[0.45 0.90 0.1 0.1],'String',{[bld.iBldStr '('  bld.nameCN ')测点层脉动风压系数，风向:' bld.dgrChar{i} '°']},'fontsize',12,'EdgeColor','none','HorizontalAlignment','center');
    set(gcf,'Units','centimeters','position',[0 2 60 30]);
    saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ')测点层脉动风压系数' bld.dgrChar{i} '.svg']);
    saveas(gcf,[bld.pathLoad 'figures/' bld.iBldStr '('  bld.nameCN ')测点层脉动风压系数' bld.dgrChar{i} '.fig']);

    close(hF1,hF2,hF3);
    disp(['Figures of ' bld.dgrChar{i} ' taps info displayed in layer are saved']);
end

end