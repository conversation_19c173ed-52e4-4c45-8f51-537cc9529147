classdef testSyncPresC < prjWTTC
%  Test of Synchronized Pressure Class 同步测压物理试验信息类
%  同时也能够代表 数值风洞模拟（试验） 类

    properties
        dataFileTail      % 数据文件后缀
        date              % '2022052301';
        dt                % 模型时间步长，为采样频率的倒数
        dtProto           % 原型时间步长矩阵，四列依次为1/10/50/100年重现期基本风压对应数值，36行对应风向，当不同风向下场地类别tc不同时会有变化
        fRatio            % 频率比
        fs                % 采样频率
        fsProto           % 原型采样频率矩阵，四列依次为1/10/50/100年重现期基本风压对应数值，36行对应风向，当不同风向下场地类别tc不同时会有变化
        iDgrProc          % 需要处理的风向角编号   ，默认为 [1:36]。在风洞试验未完成所有风向，需要提前处理部分数据时更改
        iBatch            % 与tapC中的iBatch属性定义一致，当一次测量iTst无法完成测点信息表中的iBatch时，需要将若干次iTst合并为一次iBatch
        iBlockSlct        % 当次测试中从所有记录的通道中选择的可用通道编号
        iBlockDsgn        % 选择通道对应的测点设计时的通道编号
        iTst              % 此次试验中测量的批次
        nBlock            % 试验数据中输出的阀块数量
        nChnl             % 使用的模块的通道数量总数
        nChnlPStcTot      % 静压总压测量时 使用的模块的通道数量总数
        nClmPreDat        % 数据前置列数
        nPTotal           % 总压管编号
        nPStc             % 静压管编号
        nMinSamp          % 最小数据采样样本数量
        nSel              % 选取的数据样本数量
        pStcMeanPre       % 强制静压为 某数值 或 [] （空值时采用原始数据）    
        pDynMeanAll
        pStcMeanAll 
        pTotalMeanAll
        ratiopDynTune     % 动压的调整系数，默认为 1，不调整
        snPTotStc         % 总压静压参考风压数据文件 序号
        snPStill          % 无风风压数据文件 序号
        uRefModel
        uRefProto
        tRatio            % 时间比
        tSel              % 选择的 采样时长，单位  s/秒
        txtHeaderLines    % 数据文件的文件头行数
        zRef              % 参考点高度（原型），一般位于建筑物顶部高度，总压、静压管安装高度，风压系数分母动压对应的高度
        zRefModel         % 试验中参考压皮托管的高度
    end
        
    properties(Constant)
        nChnlPer   = 64;          % number of channles per block
    end
    
    properties (Dependent)
        filePStcTot
        filePStill
        pathPMat
        pathPTxt
        pathPFig
        pathPFigPHist
        pathPFigPMean
        pathPFigPStd
    end
  
    methods
        function obj = testSyncPresC(iTst,fileTstInfo,varargin)
            defaultFlagPRefProc = 1;
            p = inputParser;
            p.addRequired('iTst', @isnumeric);  % isobject
            p.addRequired('fileTstInfo', @ischar);  % ischar
            validScalarPosNum = @(x) isnumeric(x);
            p.addParameter('flagPRefProc',defaultFlagPRefProc,validScalarPosNum);
            p.parse(iTst,fileTstInfo,varargin{:});
            flagPRefProc = p.Results.flagPRefProc;

            obj = obj@prjWTTC(fileTstInfo);
           
            switch obj.wtC
                case 1  % 物理风洞
                    tmp = readtable(['./' fileTstInfo '.xlsx'],'sheet','试验信息','Headerlines',2);
                         
                    obj.iTst           = iTst;
                    obj.date           = tmp.date{iTst};
                    obj.nPTotal        = tmp.nPTotal(iTst);
                    obj.nPStc          = tmp.nPStc(iTst);
                    obj.nMinSamp       = tmp.nMinSamp(iTst);
                    obj.nBlock         = tmp.nBlock(iTst);
                    obj.ratiopDynTune  = tmp.ratiopDynTune(iTst);
                    obj.txtHeaderLines = tmp.txtHeaderLines(iTst);
                    obj.dataFileTail   = tmp.dataFileTail{iTst};
                    obj.nClmPreDat     = tmp.nClmPreDat(iTst);
                    obj.nChnlPStcTot   = tmp.nChnlPStcTot(iTst);     % 静压总压测量时 使用的模块的通道数量总数

                    obj.zRefModel      = tmp.zRefModel(iTst);
                    obj.zRef           = obj.zRefModel*obj.scaleGeom;
                    obj.nSel           = tmp.nSel(iTst);            % 总样本中筛选出的样本数量,计算层风荷载时采用
                    obj.fs             = tmp.fs(iTst);
                    obj.dt             = 1/obj.fs;
                    obj.iDgrProc       = eval(tmp.iDgrProc{iTst});
                    obj.tSel           = obj.nSel/obj.fs;
                    obj.nChnl          = obj.nBlock*obj.nChnlPer;    % 使用的模块的通道数量总数

                    iBlockSlctStr  = num2str(tmp.iBlockSlct(iTst));
                    obj.iBlockSlct = iBlockSlctStr - '0';
                    iBlockDsgnStr  = num2str(tmp.iBlockDsgn(iTst));
                    obj.iBlockDsgn = iBlockDsgnStr - '0';
                    iBatchStr      = num2str(tmp.iBatch(iTst));  % 转换为字符串
                    obj.iBatch     = iBatchStr - '0';            % 将字符转换为数值数组
        
                    obj.pStcMeanPre    = eval(tmp.pStcMeanPre{iTst});
                    obj.snPTotStc      = findDataFileSN(obj,obj.filePStcTot);
                    obj.snPStill       = findDataFileSN(obj,obj.filePStill);
        
                    chkMkdirF(['./' obj.date '/' num2str(obj.iTst) '/measuredPresMat/'],['./' obj.date '/' num2str(obj.iTst) '/measuredPresTxt/'],...
                              ['./' obj.date '/' num2str(obj.iTst) '/figures/'],['./' obj.date '/' num2str(obj.iTst) '/figures/01测点平均风压系数/'],...
                              ['./' obj.date '/' num2str(obj.iTst) '/figures/02测点脉动风压系数/'],['./' obj.date '/' num2str(obj.iTst) '/figures/03测点风压时程/']);  %  检查是否需要创建数据处理所需的文件夹
                
                    [pStcMeanAll,pDynMeanAll,pTotalMeanAll] = pRefPrcsF(obj,'flagRun', flagPRefProc);       % 处理参考总压、静压、动压
                    obj.pStcMeanAll   = pStcMeanAll;
                    obj.pDynMeanAll   = pDynMeanAll;
                    obj.pTotalMeanAll = pTotalMeanAll;
                
                    % 执行模型缩尺比例计算并保存
                    obj.modelScaleM;                         % 计算模型试验缩尺比例并写入文件
               
                
                case 2  % 数值风洞
                    tmp = readtable(['./' fileTstInfo '.xlsx'],'sheet','模拟信息','Headerlines',2);

                    obj.uRefModel   = tmp.uRefModel;
                    obj.pDynMeanAll = 0.5*obj.rhoAir*obj.uRefModel.^2;

                    obj.zRefModel      = tmp.zRefModel;
                    obj.zRef           = obj.zRefModel*obj.scaleGeom;
                    obj.nSel           = tmp.nSel;            % 总样本中筛选出的样本数量,计算层风荷载时采用
                    obj.fs             = tmp.fs;
                    obj.dt             = 1/obj.fs;
                    obj.iDgrProc       = eval(tmp.iDgrProc{iTst});
                    obj.tSel           = obj.nSel/obj.fs;
            end
        end
        
        function obj = modelScaleM(obj)    %   计算模型试验的频率比和时间比
       
            for i = 1:obj.nDgr
                obj.uRefProto(i,:) = sqrt(muZF(obj.tc(i),obj.zRef).*obj.loadSetT.w0*1000*2/obj.rhoAir);  % 原型参考高度的风速，m/s
                obj.uRefModel(i,:) = sqrt(obj.pDynMeanAll/(0.5*obj.rhoAir));                             % 模型参考高度的风速，m/s，设置在参考高度（近建筑顶部）处测量得到的动压
                obj.tRatio(i,:)    = obj.scaleGeom.*obj.uRefModel(i)./obj.uRefProto(i,:);                % 时间比：原型/模型
                obj.fRatio(i,:)    = obj.uRefProto(i,:)./obj.scaleGeom./obj.uRefModel(i);                % 频率比：原型/模型
                obj.dtProto(i,:)   = 1./obj.fs.*obj.tRatio(i,:);                                         % 建筑原型的采样时间间隔
                obj.fsProto(i,:)   = obj.fs.*obj.fRatio(i,:);                                            % 建筑原型的采样频率
            end

        end
             
        function filePStcTot = get.filePStcTot(obj)
            filePStcTot = ['./' obj.date '/' num2str(obj.iTst) '/measuredPresTxt/总压静压'];  % 总压静压的文件头
        end

        function filePStill = get.filePStill(obj)
            filePStill = ['./' obj.date '/' num2str(obj.iTst) '/measuredPresTxt/无风'];  % 无风时的风压数据文件
        end

        function pathPMat = get.pathPMat(obj)
            pathPMat = ['./' obj.date '/' num2str(obj.iTst) '/measuredPresMat/'];
        end
        
        function pathPTxt = get.pathPTxt(obj)
            pathPTxt = ['./' obj.date '/' num2str(obj.iTst) '/measuredPresTxt/'];
        end

        function pathPFig = get.pathPFig(obj)
            pathPFig = ['./' obj.date '/' num2str(obj.iTst) '/figures/'];
        end
        
        function pathPFig = get.pathPFigPMean(obj)
            pathPFig = ['./' obj.date '/' num2str(obj.iTst) '/figures/01测点平均风压系数/'];
        end
        
        function pathPFig = get.pathPFigPStd(obj)
            pathPFig = ['./' obj.date '/' num2str(obj.iTst) '/figures/02测点脉动风压系数/'];
        end

        function pathPFig = get.pathPFigPHist(obj)
            pathPFig = ['./' obj.date '/' num2str(obj.iTst) '/figures/03测点风压时程/'];
        end
    end
    
    methods (Access = private)
        function serialNumbers = findDataFileSN(obj,fileHeader)
            % 查找以"fileHeader"开头的文件并提取序号

            filePattern = [fileHeader '*.' obj.dataFileTail];
            [~, nameHeader ,~] = fileparts(fileHeader);
            files = dir(filePattern);
            
            serialNumbers = zeros(length(files), 1);
            for i = 1:length(files)
                % 提取文件名（不含路径和扩展名）
                [~, fileName, ~] = fileparts(files(i).name);
                % 提取"总压静压"后面的数字部分
                numStr = regexp(fileName, ['^' nameHeader '(\d+)$'], 'tokens');
                if ~isempty(numStr)
                    serialNumbers(i) = str2double(numStr{1}{1});
                end
            end
            
            % 去除serialNumbers中为0的元素
            serialNumbers = serialNumbers(serialNumbers > 0);
            
            % 如果没有找到文件，给出警告
            if isempty(serialNumbers)
                disp(['未找到以"' nameHeader '"开头的文件']);
                serialNumbers = [];
            end
        end
    end
end

