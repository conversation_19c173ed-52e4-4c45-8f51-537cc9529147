function [] = tstAvgShowF(tst, avgInfo)
% 展示平均tst对象的详细信息
% 
% 输入参数:
%   tst - 平均tst对象
%   avgInfo - 平均信息结构体（可选）
%
% 示例:
%   load('.\mat\tst.mat');
%   load('.\mat\tstAvgInfo.mat');
%   tstAvgShowF(tst, avgInfo);
%
% @lichaosz 20241220

if nargin == 0
    % 如果没有输入参数，尝试加载文件
    if exist('.\mat\tst.mat', 'file') == 2
        load('.\mat\tst.mat', 'tst');
    else
        error('未找到平均tst对象文件 tst.mat');
    end
    
    if exist('.\mat\tstAvgInfo.mat', 'file') == 2
        load('.\mat\tstAvgInfo.mat', 'avgInfo');
    else
        warning('未找到平均信息文件 tstAvgInfo.mat');
        avgInfo = [];
    end
elseif nargin == 1
    if exist('.\mat\tstAvgInfo.mat', 'file') == 2
        load('.\mat\tstAvgInfo.mat', 'avgInfo');
    else
        avgInfo = [];
    end
end

disp('=== 平均TST对象详细信息 ===');
disp(' ');

% 显示基本信息
disp('1. 基本信息:');
disp(['   - 试验编号: ' num2str(tst.iTst)]);
disp(['   - 试验日期: ' tst.date]);
disp(['   - 采样频率: ' num2str(tst.fs) ' Hz']);
disp(['   - 时间步长: ' num2str(tst.dt, '%.6f') ' s']);
disp(['   - 最小采样数: ' num2str(tst.nMinSamp)]);
disp(['   - 参考高度: ' num2str(tst.zRef) ' m']);
disp(' ');

% 显示平均后的压力值
disp('2. 平均压力值:');
disp(['   - 总压平均值 (pTotalMeanAll): ' num2str(tst.pTotalMeanAll, '%.4f') ' Pa']);
disp(['   - 静压平均值 (pStcMeanAll):   ' num2str(tst.pStcMeanAll, '%.4f') ' Pa']);
disp(['   - 动压平均值 (pDynMeanAll):   ' num2str(tst.pDynMeanAll, '%.4f') ' Pa']);
disp(' ');

% 显示原始数据对比（如果有avgInfo）
if ~isempty(avgInfo)
    disp('3. 原始数据对比:');
    disp(['   - 基于 ' num2str(avgInfo.nTstUsed) ' 个原始tst对象']);
    disp(['   - 创建时间: ' avgInfo.createDate]);
    disp('   - 原始文件:');
    for i = 1:length(avgInfo.originalFiles)
        disp(['     ' avgInfo.originalFiles{i}]);
    end
    disp(' ');
    
    disp('   - 总压值对比:');
    disp(['     原始值: ' num2str(avgInfo.originalPTotalMeanAll', '%.4f') ' Pa']);
    disp(['     平均值: ' num2str(tst.pTotalMeanAll, '%.4f') ' Pa']);
    disp(['     标准差: ' num2str(std(avgInfo.originalPTotalMeanAll), '%.4f') ' Pa']);
    disp(['     变异系数: ' num2str(std(avgInfo.originalPTotalMeanAll)/mean(avgInfo.originalPTotalMeanAll)*100, '%.2f') '%']);
    disp(' ');
    
    disp('   - 静压值对比:');
    disp(['     原始值: ' num2str(avgInfo.originalPStcMeanAll', '%.4f') ' Pa']);
    disp(['     平均值: ' num2str(tst.pStcMeanAll, '%.4f') ' Pa']);
    disp(['     标准差: ' num2str(std(avgInfo.originalPStcMeanAll), '%.4f') ' Pa']);
    disp(' ');
    
    disp('   - 动压值对比:');
    disp(['     原始值: ' num2str(avgInfo.originalPDynMeanAll', '%.4f') ' Pa']);
    disp(['     平均值: ' num2str(tst.pDynMeanAll, '%.4f') ' Pa']);
    disp(['     标准差: ' num2str(std(avgInfo.originalPDynMeanAll), '%.4f') ' Pa']);
    disp(['     变异系数: ' num2str(std(avgInfo.originalPDynMeanAll)/mean(avgInfo.originalPDynMeanAll)*100, '%.2f') '%']);
    disp(' ');
end

% 显示模型缩尺参数
disp('4. 模型缩尺参数:');
if ~isempty(tst.uRefModel)
    if isnumeric(tst.uRefModel) && isscalar(tst.uRefModel)
        disp(['   - 模型参考风速 (uRefModel): ' num2str(tst.uRefModel, '%.4f') ' m/s']);
    elseif isnumeric(tst.uRefModel) && isvector(tst.uRefModel)
        disp(['   - 模型参考风速 (uRefModel): ' num2str(tst.uRefModel(1), '%.4f') ' m/s (第1风向)']);
        disp(['     向量长度: ' num2str(length(tst.uRefModel))]);
    elseif iscell(tst.uRefModel)
        disp('   - 模型参考风速 (uRefModel): 存储为cell数组');
    else
        disp('   - 模型参考风速 (uRefModel): 复杂数据类型');
    end
else
    disp('   - 模型参考风速 (uRefModel): 未定义');
end

if ~isempty(tst.tRatio)
    if isnumeric(tst.tRatio) && ismatrix(tst.tRatio)
        [nDgr, nRP] = size(tst.tRatio);
        disp(['   - 时间比例 (tRatio): ' num2str(nDgr) '×' num2str(nRP) ' 矩阵']);
        disp(['     示例值 (第1风向,50年): ' num2str(tst.tRatio(1,3), '%.6f')]);
    else
        disp('   - 时间比例 (tRatio): 复杂数据类型');
    end
else
    disp('   - 时间比例 (tRatio): 未定义');
end

if ~isempty(tst.fRatio)
    if isnumeric(tst.fRatio) && ismatrix(tst.fRatio)
        [nDgr, nRP] = size(tst.fRatio);
        disp(['   - 频率比例 (fRatio): ' num2str(nDgr) '×' num2str(nRP) ' 矩阵']);
        disp(['     示例值 (第1风向,50年): ' num2str(tst.fRatio(1,3), '%.6f')]);
    else
        disp('   - 频率比例 (fRatio): 复杂数据类型');
    end
else
    disp('   - 频率比例 (fRatio): 未定义');
end

if ~isempty(tst.dtProto)
    if isnumeric(tst.dtProto) && ismatrix(tst.dtProto)
        [nDgr, nRP] = size(tst.dtProto);
        disp(['   - 原型时间步长 (dtProto): ' num2str(nDgr) '×' num2str(nRP) ' 矩阵']);
        disp(['     示例值 (第1风向,50年): ' num2str(tst.dtProto(1,3), '%.6f') ' s']);
    else
        disp('   - 原型时间步长 (dtProto): 复杂数据类型');
    end
else
    disp('   - 原型时间步长 (dtProto): 未定义');
end
disp(' ');

% 显示数据质量评估
if ~isempty(avgInfo)
    disp('5. 数据质量评估:');
    
    % 动压变异系数评估
    cvDyn = std(avgInfo.originalPDynMeanAll)/mean(avgInfo.originalPDynMeanAll)*100;
    if cvDyn < 5
        dynQuality = '优秀';
    elseif cvDyn < 10
        dynQuality = '良好';
    elseif cvDyn < 15
        dynQuality = '一般';
    else
        dynQuality = '较差';
    end
    disp(['   - 动压重现性: ' dynQuality ' (变异系数: ' num2str(cvDyn, '%.2f') '%)']);
    
    % 总压变异系数评估
    cvTotal = std(avgInfo.originalPTotalMeanAll)/mean(avgInfo.originalPTotalMeanAll)*100;
    if cvTotal < 5
        totalQuality = '优秀';
    elseif cvTotal < 10
        totalQuality = '良好';
    elseif cvTotal < 15
        totalQuality = '一般';
    else
        totalQuality = '较差';
    end
    disp(['   - 总压重现性: ' totalQuality ' (变异系数: ' num2str(cvTotal, '%.2f') '%)']);
    
    disp(' ');
    disp('   评估标准:');
    disp('   - 优秀: 变异系数 < 5%');
    disp('   - 良好: 变异系数 5-10%');
    disp('   - 一般: 变异系数 10-15%');
    disp('   - 较差: 变异系数 > 15%');
    disp(' ');
end

% 显示使用建议
disp('6. 使用建议:');
disp('   - 该平均tst对象可直接用于后续的风荷载计算');
disp('   - 压力值已基于多次试验取平均，提高了数据可靠性');
disp('   - 其他属性保持tst01的原始值，确保参数一致性');
disp('   - 建议保存原始的tst01-tst04文件作为备份');
disp(' ');

disp('=== 展示完成 ===');

end
