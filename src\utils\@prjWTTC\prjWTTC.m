classdef prjWTTC < handle
% project Wind Tunnel Test Class 风洞试验工程项目的基本信息
%   此处显示详细说明
    
    properties
        caseRWD          % 'scut' | 风速风向折减的方案，cabr,scut
        city
        district
        docTpltRpt
        gCode
        loadSetT      % 不同重现期的荷载集合表，基于广东省荷载规范，重现期依次为 1 10 50 100年
        namePrjCN
        namePrjEN
        nameBldAllCN
        nameBldAllEN
        nClmSubFig   % 所有风向下结果绘制在一张图中时subFig的列数
        nRowSubFig   
        nBld
        nDgr
        nTst
        pathAst      % 项目辅助文件路径
        pathMat      % 项目mat数据路径
        pathSbm      % 项目提交数据路径
        RP           % 当前计算的重现期，单一整数，是影响其他相关参数的启动变量，w0等
        RPAcc        % 舒适度验算荷载重现期，单一整数
        RPLoc        % 围护结构设计荷载重现期，单一整数
        RPMain       % 主体结构承载力校核荷载重现期，单一整数
        RPLoop       % 临时循环计算选择的荷载重现期，[10 50]
        RWDSet       % 当前项目风向影响系数的固定数据集
        scaleGeom
        swWIV
        swEvlp
        tc
        typeStrct     % 1 为高层，2 为屋盖
        wtC          % 风洞试验类型：1 物理，2 数值
        xlsTpltLocPres
        xlsTpltRpt
        zetaAcc
        zetaMain
    end
    
    properties(Constant)
        fmtFigOut       = 'svg';   % 输出图片的格式
        rhoAir          = 1.25    % 空气密度 kg/m^3，规范中是 1.25 后边改过来，不再用1.225
        gAcc            = 9.78;    % 重力加速度，单位 m/s^2
        sheetSum        = '项目信息';
        sheetStructInfo = '结构信息';
        sheetAcc        = '舒适度';
        sheetLoadRcmdS  = '建议风荷载';
        sheetLoadRcmdF  = '建议风荷载+层剪力+层倾覆弯矩';
        sheetLoadRcmdC  = '规范风荷载对比';
        sheetStoryDrift = '层间位移角';
    end
    
	properties (Dependent)
        dDgr
        dgr
        iRPSet        % 当前荷载重现期RP在RPSet中的序号
        % iRPLoop       % 当前荷载重现期RP在RPLoop中的序号
        dgrChar
        RPSet         % [1 10 50 100] 重现期固定数据集，这其中的顺序是不能改变的
        RWD           % 由当前RP确定
        typeStrctStr
        w0            % 当前荷载重现期对应的基本风压
        w0Acc
        w0Loc
        w0Main
    end
    
    methods
        function obj = prjWTTC(fileTstInfo)
            filePathTstInfo = ['./' fileTstInfo '.xlsx'];
            if exist(filePathTstInfo,'file') == 2
                tmp  = readtable(filePathTstInfo,'sheet','项目信息','Headerlines',2);
            else
                warning(['文件' filePathTstInfo '不存在']);
                return;
            end
           
            obj.namePrjCN   = tmp.namePrjCN{1};
            obj.namePrjEN   = tmp.namePrjEN{1};
            obj.city        = tmp.city{1};
            obj.district    = tmp.district{1};
            obj.scaleGeom   = tmp.scaleGeom(1);
            obj.nDgr        = tmp.nDgr(1);
            obj.swWIV       = tmp.swWIV(1);
            obj.swEvlp      = tmp.swEvlp(1);
            obj.typeStrct   = tmp.typeStrct(1);
            obj.zetaAcc     = tmp.zetaAcc(1);
            obj.zetaMain    = tmp.zetaMain(1);
            obj.RPLoc       = tmp.RPLoc(1);
            obj.RPAcc       = tmp.RPAcc(1);
            obj.RPMain      = tmp.RPMain(1);
            obj.gCode       = tmp.gCode(1);
            obj.caseRWD     = tmp.caseRWD{1};
            obj.wtC         = tmp.wtC(1);
            obj.nameBldAllCN = rmmissing(tmp.nameBldAllCN);
            obj.nameBldAllEN = rmmissing(tmp.nameBldAllEN);
            obj.nBld        = length(obj.nameBldAllCN);
            obj.xlsTpltRpt     = '..\#Templates\0风洞试验报告\高层主体结构测压试验结果模板.xlsx';
            obj.xlsTpltLocPres = '..\#Templates\0风洞试验报告\测点风荷载统计模板.xlsx';
            obj.docTpltRpt     = '..\#Templates\0风洞试验报告\matlab保存高保真图片A4模板.dotx';
            obj.pathMat        = './mat/';          % 项目mat数据路径
            obj.pathAst        = './00辅助文件/';    % 项目辅助文件路径
            obj.pathSbm        = './00提交甲方/';    % 项目辅助文件路径
            chkMkdirF(obj.pathMat,obj.pathAst,obj.pathSbm);

            switch obj.wtC
                case 1   % 物理风洞
                    tmp2     = readtable(filePathTstInfo,'sheet','试验信息','Headerlines',2);
                    obj.nTst = length(tmp2.iTst);
                case 2   % 数值风洞
                    obj.nTst = 1;
            end

            switch obj.nDgr
                case 36
                    obj.nClmSubFig = 9;
                case 24
                    obj.nClmSubFig = 8;
                case 8
                    obj.nClmSubFig = 4;
            end
            obj.nRowSubFig = ceil(obj.nDgr/obj.nClmSubFig);

            obj.loadSetT = basicWP4CityF(obj.city,obj.district,obj.rhoAir);   % 获取基本风压

            obj.RWDSet = loadRWDF(obj,'flgRereadXls',1);   % 导入风向影响系数数据库

            if isscalar(tmp.tc)
                obj.tc(1:obj.nDgr) = tmp.tc(1);
            elseif length(tmp.tc) == 36
                obj.tc = tmp.tc;
            else
                warning(['项目场地类别信息 tc 的输入数据长度需为 1 或 ' num2str(obj.dDgr)]);
            end

        end
        
        function w0 = get.w0(obj)
            w0 = obj.loadSetT.w0(obj.loadSetT.RP==obj.RP);   % 由主体结构设计年限决定基本风压
        end

        function w0Acc = get.w0Acc(obj)
            w0Acc = obj.loadSetT.w0(obj.loadSetT.RP==obj.RPAcc);   % 由主体结构设计年限决定基本风压
        end

        function w0Loc = get.w0Loc(obj)
            w0Loc = obj.loadSetT.w0(obj.loadSetT.RP==obj.RPLoc);   % 由主体结构设计年限决定基本风压
        end

        function w0Main = get.w0Main(obj)
            w0Main = obj.loadSetT.w0(obj.loadSetT.RP==obj.RPMain);   % 由主体结构设计年限决定基本风压
        end

        function iRPSet = get.iRPSet(obj)
            iRPSet = obj.loadSetT.iRPSet(obj.loadSetT.RP == obj.RP);
        end
        
        function RPSet = get.RPSet(obj)
            RPSet = obj.loadSetT.RP;
        end

        function RWD = get.RWD(obj)
            RWD = obj.RWDSet{:,obj.iRPSet+1};   % 第一列为风向角
        end

        function dgrChar = get.dgrChar(obj)
            dgrChar = cell([obj.nDgr,1]);
            for i = 1:obj.nDgr
                dgrChar{i} = num2str(floor((i-1)*obj.dDgr),'%03i');
            end
        end

        function dDgr = get.dDgr(obj)
            dDgr = 360/obj.nDgr;        % 风向角间隔，不需改动参数
        end

        function dgr = get.dgr(obj)
            dgr = zeros(obj.nDgr,1);
            for i = 1:obj.nDgr
                dgr(i) = floor((i-1)*obj.dDgr);
            end
        end

        function typeStrctStr = get.typeStrctStr(obj)
            switch obj.typeStrct
                case 1
                    typeStrctStr = '超高层';
                case 2
                    typeStrctStr = '大跨屋盖';
            end
        end
        
    end
end

