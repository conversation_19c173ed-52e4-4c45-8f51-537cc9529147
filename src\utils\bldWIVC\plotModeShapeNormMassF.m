function hP = plotModeShapeNormMassF(bld,z,phi,iTwr,n)
% 绘制结构模态的质量归一化振型
% 输入:
% bld  -- 结构对象
% n    -- 模态数
% 输出:
% hP   -- 绘图句柄

markerS = 'dosx+>*p';

for i = 1:n
    figure
    for j = 1:unique(iTwr)
        phiX = phi(1:3:end,i);  %bld.flrT.iTwr==j
        phiY = phi(2:3:end,i);
        phiZ = phi(3:3:end,i);
        % z    = bld.flrT.z(bld.flrT.iTwr==j);
        k    = (j-1)*3;
        hP(k+1) = plot(phiX(iTwr==j),z,'k','marker',markerS(j)); hold on; lgdStr{k+1} = ['\phi_X 塔' xlscol(j)]; grid on;
        hP(k+2) = plot(phiY(iTwr==j),z,'r','marker',markerS(j)); hold on; lgdStr{k+2} = ['\phi_Y 塔' xlscol(j)];
        hP(k+3) = plot(phiZ(iTwr==j),z,'b','marker',markerS(j)); hold on; lgdStr{k+3} = ['\phi_Z 塔' xlscol(j)];
    end


    setMyFig(gcf,'质量归一化振型','楼层高度',['模态' num2str(i,'%02d')],[],12,hP,lgdStr,[.68,.18,.1,.1],...
     [11 2 8 12],[bld.pathStruDyn 'figures/' bld.iBldStr '('  bld.nameCN ')质量归一化振型-' num2str(i,'%02d') '.svg'],[],[],[],[]);


end

end

